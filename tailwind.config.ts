/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        jakarta: ['"Plus Jakarta Sans"', 'sans-serif'],
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic": "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        "publish-btn-gradient": "linear-gradient(to right, #6F923E, #A1C45E)",
        "home-gradient": "linear-gradient(135deg, #CAE2FF 0%, #FAF3E4 25%, #E2CAFF 75%, #FFFFFF 100%)",
        "donation-gradient": "linear-gradient(135deg, #CAE5FF 0%, #FFFFFF 35%, #FCD6C6 65%, #FFFFFF 100%)",
      },
      colors: {
        'label-color': '#4b5563',
        'color-grey': '#F5F5F7',
        'color-grey-1': '#9C9CA4',
        'color-grey-2': '#DFDFDF',
        'color-grey-3': '#484848',
        'color-grey-4': '#666666',
        'color-grey-5': '#EEEEEE',
        'color-grey-6': '#7B7B7B',
        'color-grey-7': '#777777',
        'color-grey-8': '#FCFCFC',
        'color-grey-9': '#E1E1E1',
        'color-grey-10': '#F3F3F3',
        'color-grey-11': '#808080',
        'color-grey-12': '#909090',
        'color-grey-13': '#B3B3B3',
        'color-grey-14': '#565656',
        'color-grey-15': '#8E8E93',
        'color-grey-16': '#9595AC',
        'color-grey-17': '#EAEAEA',
        'color-grey-18': '#5D5D5D',
        'color-grey-19': '#848484',
        'color-red': '#D0323A',
        'color-red-1': '#db5962',
        'color-red-2': '#FF4C5E',
        'color-red-3': '#B3261E',
        'color-black': '#141522',
        'color-black-2': '#000000',
        'color-black-3': '#0F0F0F',
        'color-black-4': '#0C0C0D',
        'color-yellow': '#FFBB54',
        'color-yellow-1': '#FFD140',
        'color-yellow-2': '#e79a26',
        'color-yellow-3': '#FFE9C8',
        'color-blue': '#005964',
        'color-blue-1': '#04A4F4',
        'color-blue-2': '#0474F4',
        'color-blue-3': '#034FA6',
        'color-blue-4': '#EAF8FF',
        'color-blue-5': '#74AFF2',
        'color-blue-6': '#007AFF',
        'color-blue-7': '#E8EEF6',
        'color-purple': '#910F9F',
        'color-purple-2': '#6F2877',
        'color-green': '#67b338',
        'color-green-1': '#6F923E',
        'color-green-2': '#0AB374',
        'color-green-3': '#43A574',
        'color-green-4': '#25C78B',
        'color-green-5': '#E3EDEA',
        'color-green-6': '#43A574',
        'color-green-7':'#E9FFF8',
        'color-cream': '#FFFEFD',
        'color-cream-1': '#FFF8E5',
        'color-cream-2': '#FFF9E8',
        'color-pink': '#FFEFE8',
        'color-orange': '#FF854B',
        'color-orange-2': '#F6EAE6',
        'color-brown': '#844B46',
        'neutral-white': '#FFFFFF'
      },
      keyframes: {
        slideCircular: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
      },
      animation: {
        slideCircular: 'slideCircular 15s linear infinite',
      },
    },
  },
  darkMode: "class",
  plugins: [
    function ({ addUtilities }: { addUtilities: any }) {
      addUtilities({
        '.no-scrollbar': {
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          'scrollbar-width': 'none',
          '-ms-overflow-style': 'none',
        },
      });
    },
  ],
};

export default config;