'use client'
import MainLoader from '@/components/MainLoader/MainLoader'
import { useApi } from '@/hooks/useApi'
import { getAccountById, handleEmailLogin } from '@/lib/actions/account.actions'
import {
  getToken,
  getSessionKey,
  handleAuthResponse,
  logout,
} from '@/lib/actions/login.actions'
import { usePathname, useRouter } from 'next/navigation'
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'
import ProgressBar from '@/components/ProgressBar/ProgressBar'
import { useDispatch } from 'react-redux'
import { setCartCount, setFollowedTeachers, setPaymentToken } from '@/lib/redux'
import { toast } from 'react-toastify'
import { getAllFollowedTeachers } from '@/lib/actions/follow.actions'
import { ROLES } from '@/types/enum'
import { useCheckProfileCompletion } from '@/hooks/useCheckProfileCompletion'
import useModal from '@/hooks/useModal'
import CustomModal from '@/components/CustomComponents/CustomModal/CustomModal'
import CustomModalIcon from '@/components/Icons/CustomModalIcon'
import { purchaseMembership } from '@/lib/actions/membership.actions'

const AuthContext = createContext<any>(undefined)

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<any>(null)
  const [followingResponse, fetchFollowing] = useApi((access: string) =>
    getAllFollowedTeachers(access),
  )
  const pathname = usePathname()
  const router = useRouter()
  const dispatch = useDispatch()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const isFollowingAPIcalled = useRef<boolean>(false)
  const [response, getAccount] = useApi((access: string, userId: string) =>
    getAccountById(access, userId),
  )
  const [purchaseResponse, chooseMembership] = useApi(
    (access: string, productId: string, type: 'Monthly' | 'Annual') =>
      purchaseMembership(access, productId, type),
    false,
  )
  const [loginDisableModal, showLoginDisableModal] = useModal()

  const checkProfileCompletion = useCheckProfileCompletion()

  const getCookie = (name: string): string | undefined => {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop()?.split(';').shift()
  }
  const stackAccess = getCookie('stack-access')

  useEffect(() => {
    if (followingResponse.isSuccess) {
      dispatch(setFollowedTeachers(followingResponse.data?.data || []))
      isFollowingAPIcalled.current = true
    }
  }, [followingResponse, dispatch])

  useEffect(() => {
    if (response.isSuccess) {
      const userData = response.data?.data
      dispatch(setCartCount(userData?.Cart_Item_Count__c))
      ;(async () => {
        if (userData?.Login_Allowed__c === false) {
          handleLoginDisable()
          return
        }
        const userProfileData = await handleAuthResponse(userData)
        setUser(userProfileData)
        if (
          userProfileData?.role !== ROLES.Teacher &&
          !isFollowingAPIcalled.current
        ) {
          await fetchFollowing()
        }
      })()
    }
  }, [response])

  useEffect(() => {
    if (stackAccess) {
      GetDetails()
    } else {
      handleSessionLogout()
    }
  }, [])

  useEffect(() => {
    if (user && user?.role !== ROLES.Teacher && !isFollowingAPIcalled.current) {
      ;(async () => {
        await fetchFollowing()
      })()
    }
  }, [user, pathname])

  useEffect(() => {
    if (purchaseResponse.isSuccess) {
      dispatch(setPaymentToken(purchaseResponse.data?.data?.token))
      router.push(`/checkout?token=${purchaseResponse.data?.data?.token}`)
    }
  }, [purchaseResponse])

  const handleLoginDisable = () => {
    showLoginDisableModal({
      title: '',
      contentFn: (onClose: any) => (
        <CustomModal
          isLoading={false}
          desc="Your account is disabled. Please contact support."
          icon={<CustomModalIcon />}
          title="Account Disabled"
          showBtn={false}
        />
      ),
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }

  const GetDetails = useCallback(async () => {
    setIsLoading(true)
    try {
      const accessToken = await getToken()
      const userSession = await getSessionKey('user')
      if (!accessToken) return
      if (userSession) {
        await getAccount(JSON.parse(userSession)?.id)
      } else {
        const userData = await checkProfileCompletion()
        if (!userData) return
        if (userData?.Login_Allowed__c === false) {
          handleLoginDisable()
          return
        }
        const userProfileData = await handleAuthResponse(userData)
        if (
          userProfileData?.role !== ROLES.Teacher &&
          !isFollowingAPIcalled.current
        ) {
          await fetchFollowing()
        }
        setUser(userProfileData)
        if (userData != null) {
          const redirectUrl = localStorage.getItem('redirect')
          let membershipToken: any = localStorage.getItem('subscriptionPlan')
          membershipToken = membershipToken ? JSON.parse(membershipToken) : null
          if (membershipToken) {
            localStorage.removeItem('subscriptionPlan')
            chooseMembership(membershipToken?.Id, membershipToken?.Type)
          } else {
            localStorage.removeItem('redirect')
            router.push(redirectUrl ? redirectUrl : '/dashboard')
          }
        }
        return
      }
    } catch (error) {
      console.error('Error fetching user details:', error)
    } finally {
      setIsLoading(false)
    }
  }, [checkProfileCompletion, getAccount, router])

  const handleSessionLogout = async () => {
    const accessToken = await getToken()
    const userSession = await getSessionKey('user')
    if (!accessToken && userSession) {
      toast.error('Session expired, please login')
      await logout()
      router.push('/sign-in')
      return
    }
  }

  if (isLoading && stackAccess) {
    return <MainLoader />
  }

  return (
    <AuthContext.Provider value={{ user, setUser, isLoading }}>
      <ProgressBar />
      {children}
      {loginDisableModal}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
