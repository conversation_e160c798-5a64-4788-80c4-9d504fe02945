import React, { useEffect, useRef, useState } from 'react'
import Otp from './Otp'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import axios from 'axios'
import ReCAPTCHA from 'react-google-recaptcha'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'
import { useDispatch, useSelector } from 'react-redux'
import {
  setUserPhoneNumber,
  userPhoneNumber,
} from '@/lib/redux/slices/metaSlice'
import {
  isPhoneAlreadyExist,
  signInByPhone,
} from '@/lib/actions/account.actions'
import { handleAuthResponse } from '@/lib/actions/login.actions'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { loginRequestOtp } from '@/lib/auth'
import { useApi } from '@/hooks/useApi'
import useModal from '@/hooks/useModal'
import CustomModal from '../CustomComponents/CustomModal/CustomModal'
import CustomModalIcon from '../Icons/CustomModalIcon'

interface MobileLoginProps {
  showOtpBtn?: boolean
  setIsPhoneVerified?: any
  setUserPhone?: any
  isVerificationRequired?: boolean
  classNames?: string
  title?: string
  isMobileVerificationRequired?: boolean
}

const MobileLogin: React.FC<MobileLoginProps> = ({
  showOtpBtn = true,
  setIsPhoneVerified,
  setUserPhone,
  isVerificationRequired = true,
  classNames = '',
  title = 'Mobile',
  isMobileVerificationRequired = false,
}) => {
  const router = useRouter()
  const dispatch = useDispatch()
  const [phone, setPhone] = useState<string>('')
  const [showOtp, setShowOtp] = useState<boolean>(false)
  const [showCaptcha, setShowCaptcha] = useState<boolean>(false)
  const [token, setToken] = useState<string | null>('')
  const [error, setError] = useState<string | null>(null)
  const recaptchaRef = useRef<ReCAPTCHA>(null)
  const [isVerified, setIsVerified] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const phoneNumber = useSelector(userPhoneNumber)
  const [loginDisableModal, showLoginDisableModal] = useModal()
  const [phoneNumberError, setPhoneNumberError] = useState<string | null>(null)
  const [isMobileAlreadyExistResponse, checkIsMobileAlreadyExistResponse] =
    useApi((phone: string) => isPhoneAlreadyExist(phone), false)

  const handleCaptchaSubmission = async (token: string | null) => {
    if (token) {
      setToken(token)
      setShowCaptcha(false)
      setIsVerified(true)
    } else {
      setIsVerified(false)
    }
  }

  useEffect(() => {
    if (isMobileAlreadyExistResponse.isSuccess) {
      if (isMobileAlreadyExistResponse.data?.data) {
        setPhoneNumberError(
          'This phone number is already registered. Please sign in.',
        )
      } else {
        setPhoneNumberError(null)
      }
    }
  }, [isMobileAlreadyExistResponse])

  useEffect(() => {
    if (phone && isMobileVerificationRequired) {
      handleIsMobileAlreadyExists(phone)
    }
  }, [phone])

  const handleChangePhone = (value: string) => {
    !isVerificationRequired && setUserPhone(value)
    setPhone(value)
  }

  const handleOTP = async () => {
    if (!isVerified) {
      toast.error('Please verify captcha.', { autoClose: 1000 })
      return
    }
    if (!phone || phone.length < 10) {
      setError('Please enter a valid phone number')
      return
    }
    setError(null)
    setIsLoading(true)
    try {
      await loginRequestOtp(`+${phone}`, 'sms', token)
      setShowOtp(true)
      setIsLoading(false)
    } catch (err) {
      setIsLoading(false)
      toast.error('Something went wrong!' + err, { autoClose: 1500 })
    }
  }

  const handleIsMobileAlreadyExists = async (phone: string) => {
    if (phone.length < 12) return
    checkIsMobileAlreadyExistResponse('+' + phone)
  }

  const handleLoginDisable = () => {
    showLoginDisableModal({
      title: '',
      contentFn: (onClose: any) => (
        <CustomModal
          isLoading={false}
          desc="Your account is disabled. Please contact support."
          icon={<CustomModalIcon />}
          title="Account Disabled"
          showBtn={false}
        />
      ),
      closeOnClickOutside: true,
      size: 'md',
      showCloseButton: false,
    })
  }

  const handleOtpSubmit = async (otp: string) => {
    setIsLoading(true)
    try {
      // Sign in with phone and OTP via NextAuth credentials provider
      const result = await axios.post('/api/otp/verify', {
        identifier: `+${phone}`,
        otp: otp,
      })
      if (result?.status == 200 && showOtpBtn) {
        const userDetails = await signInByPhone(`+${phone}`)
        if (userDetails?.Login_Allowed__c === false) {
          handleLoginDisable()
          return
        }
        if (userDetails) {
          await handleAuthResponse(userDetails)
          router.push('/dashboard')
        } else {
          router.push('/fill-details')
        }
      } else {
        setIsPhoneVerified(true)
        setUserPhone(`+${phone}`)
      }
      setIsLoading(false)
      dispatch(setUserPhoneNumber(`+${phone}`))
    } catch (err) {
      toast.error('Something went wrong!' + err, { autoClose: 1500 })
      setIsLoading(false)
    }
  }

  return (
    <div className="flex flex-col items-stretch">
      {!showOtp && (
        <>
          <label
            className={`${classNames ? classNames : 'text-sm font-medium label-color mb-1'}`}
            htmlFor="mobile"
          >
            {title}
          </label>
          <div className="relative">
            <PhoneInput
              country={'us'}
              value={phoneNumber || phone}
              onChange={handleChangePhone}
              inputStyle={{ width: '100%' }}
              containerStyle={{ marginBottom: '1rem' }}
            />
            {!showOtpBtn &&
              phone != '' &&
              !phoneNumber &&
              isVerificationRequired &&
              !phoneNumberError && (
                <>
                  {isLoading ? (
                    <div className="absolute right-3 top-3">
                      <div className="delete-icon-loader"></div>
                    </div>
                  ) : (
                    <span
                      className="absolute right-2 top-2 text-sm font-semibold text-color-blue-1 cursor-pointer"
                      onClick={handleOTP}
                    >
                      Verify Phone Number
                    </span>
                  )}
                </>
              )}
          </div>
        </>
      )}
      {phoneNumberError && (
        <span className="text-red-500 font-semi-bold text-sm">
          {phoneNumberError}
        </span>
      )}
      {error && <div className="text-red-500 text-sm mb-2">{error}</div>}
      {showOtp && (
        <Otp
          onOtpSubmit={handleOtpSubmit}
          isLoading={isLoading}
          setShowCaptcha={setShowCaptcha}
          showCaptcha={showCaptcha}
          phone={`+${phone}`}
          setShowOtp={setShowOtp}
          handleOTP={() => handleOTP()}
          showOtpBtn={showOtpBtn}
        />
      )}
      {(!showOtp || showCaptcha) &&
        !phoneNumber &&
        phone != '' &&
        isVerificationRequired &&
        !phoneNumberError && (
          <div className="w-full flex items-center justify-center">
            <ReCAPTCHA
              sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || ''}
              ref={recaptchaRef}
              onChange={handleCaptchaSubmission}
              onExpired={() => setIsVerified(false)}
              className="w-full"
            />
          </div>
        )}
      {showOtpBtn && (
        <div className="w-full mt-3">
          {!showOtp && (
            <CustomButton
              title={'Send OTP'}
              onClick={handleOTP}
              isVerified={isVerified}
              isLoading={isLoading}
            />
          )}
        </div>
      )}
      {loginDisableModal}
    </div>
  )
}

export default MobileLogin
