import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import { getPresignedPutUrl, getPublicUrl } from '@/lib/aws'
import { updateAccountById } from '@/lib/actions/account.actions'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'
import { useDispatch, useSelector } from 'react-redux'
import { setProfilePhoto, profilePhoto } from '@/lib/redux'
import BlueUploadIcon from '../Icons/BlueUploadIcon'
import { useApi } from '@/hooks/useApi'
import { getNameLetter } from '@/utils/utils'
import { useUser } from '@stackframe/stack'

const ProfileUpload = ({ userData }: { userData: any }) => {
  const [url, setUrl] = useState<string>(userData?.photo || '')
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const { user, setUser, isLoading } = useAuth()
  const stackUser = useUser()
  const dispatch = useDispatch()
  const profileImage = useSelector(profilePhoto)
  const userImage = (userData?.photo as string).split('/')
  const [response, upload] = useApi((access: string, url) =>
    updateAccountById(access, { PhotoUrl: url }),
  )

  useEffect(() => {
    if (response.isSuccess) {
      setUser({
        ...user,
        photo: url,
      })
      dispatch(setProfilePhoto(url))
    }
  }, [response])

  const handleFileChange = async (event: any) => {
    const file = event.target.files?.[0]
    const timeStamp = new Date().getTime()
    if (file) {
      const fileName = file?.name + timeStamp
      try {
        setIsUploading(true)
        const presignedUrl = await getPresignedPutUrl(fileName, file.type)
        const uploadResponse = await fetch(presignedUrl, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': file.type,
          },
        })
        if (uploadResponse?.ok) {
          const imageLink = await getPublicUrl(fileName)
          setUrl(imageLink)
          await upload(imageLink)
          toast.success('Image uploaded successfully!', { autoClose: 1500 })
        }
      } catch (error) {
        toast.error('Something went wrong!' + error, { autoClose: 1500 })
      } finally {
        setIsUploading(false)
      }
    }
  }

  const renderProfileImage = (width: number, height: number) => {
    return (
      <>
        {userImage[userImage.length - 1] != 'null' ||
        (stackUser && stackUser?.profileImageUrl) ? (
          <Image
            width={width}
            height={height}
            src={
              profileImage != ''
                ? profileImage
                : userImage[userImage.length - 1] != 'null'
                  ? userData?.photo
                  : stackUser?.profileImageUrl
            }
            alt="User avatar"
            className="rounded-full w-full h-full object-cover"
          />
        ) : (
          <div
            className="rounded-full bg-black text-white flex items-center justify-center font-semibold text-base w-full h-full"
            style={{ height: `${height}px`, width: `${width}px` }}
          >
            <h3>{getNameLetter(userData?.name)}</h3>
          </div>
        )}
      </>
    )
  }

  return (
    <div className="grid grid-cols-10 gap-5 md:gap-10 items-center">
      <div className="col-span-4 md:col-span-2">
        <div className="relative w-24 h-24">
          {renderProfileImage(96, 96)}
          {(isUploading || isLoading) && (
            <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500"></div>
            </div>
          )}
        </div>
      </div>
      <div className="flex items-start justify-start gap-2 flex-col col-span-6 md:col-span-8">
        <div className="bg-white rounded-full ml-5">
          <input
            type="file"
            accept="image/*"
            className="hidden"
            id="file-upload"
            onChange={handleFileChange}
          />
          <label
            htmlFor="file-upload"
            className="cursor-pointer flex items-center gap-1 bg-color-blue-4 px-4 py-3 rounded-lg border-2 border-dotted border-color-blue-2 w-full justify-start"
          >
            <BlueUploadIcon />
            <h2 className="font-medium text-color-blue-2 text-sm w-full">
              Upload Photo
            </h2>
          </label>
        </div>
        <span className="text-color-grey-1 text-xs">
          Only .jpg, .jpeg, and .png • files 600x600 and max 2 MB
        </span>
      </div>
    </div>
  )
}

export default ProfileUpload
