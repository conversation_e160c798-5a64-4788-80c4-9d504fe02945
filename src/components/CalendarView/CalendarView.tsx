'use client'
import React, { useEffect, useRef, useState } from 'react'
import { format, addWeeks, addMonths } from 'date-fns'
import PrevNextButton from '../PrevNextButton/PrevNextButton'
import Week from '../Icons/Week'
import Month from '../Icons/Month'
import WeekView from './WeekView'
import MonthView from './MonthView'
import { useIsMobile } from '@/hooks/useIsMobile'

interface CalenderViewProps {
  paramGeners: string | null
  paramLevels: string | null
  paramTypes: string | null
  classCategory: string | null
  category: 'All' | 'Adult Class' | 'Youth Class'
  selectedType: string
  selectedGenre: string[]
  selectedLevel: string[]
  selfClasses?: boolean
  activeTab?: string
}

const CalenderView = ({
  paramGeners,
  paramLevels,
  paramTypes,
  classCategory,
  category,
  selectedType,
  selectedGenre,
  selectedLevel,
  selfClasses,
  activeTab,
}: CalenderViewProps) => {
  const [weeklyView, setWeeklyView] = useState<boolean>(true)
  const calendarRef = useRef<any>(null)
  const [currentDate, setCurrentDate] = useState(new Date())
  const isMobile = useIsMobile()

  useEffect(() => {
    const url = new URL(window.location.href)
    const viewParam = url.searchParams.get('calendar-view')
    if (viewParam === 'week') {
      setWeeklyView(true)
    } else if (viewParam === 'month') {
      setWeeklyView(false)
    }
  }, [])

  useEffect(() => {
    if (isMobile) {
      setWeeklyView(true)
      const url = new URL(window.location.href)
      url.searchParams.set('calendar-view', 'week')
      window.history.pushState({}, '', url.toString())
    }
  }, [isMobile])

  // Navigate between weeks
  const handlePrevWeek = () => setCurrentDate((prev) => addWeeks(prev, -1))
  const handleNextWeek = () => setCurrentDate((prev) => addWeeks(prev, 1))

  // Navigate between months
  const handlePrevMonth = () => {
    if (calendarRef.current) {
      const calendarApi = (calendarRef.current as any)?.getApi()
      calendarApi.prev()
    }
    setCurrentDate((prev) => addMonths(prev, -1))
  }
  const handleNextMonth = () => {
    if (calendarRef.current) {
      const calendarApi = (calendarRef.current as any)?.getApi()
      calendarApi.next()
    }
    setCurrentDate((prev) => addMonths(prev, 1))
  }

  const handlePrevNextClick = () => {
    if (weeklyView) {
      return { onPrev: handlePrevWeek, onNext: handleNextWeek }
    } else {
      return { onPrev: handlePrevMonth, onNext: handleNextMonth }
    }
  }

  const handleViewChange = (isWeekly: boolean) => {
    setWeeklyView(isWeekly)
    const url = new URL(window.location.href)
    url.searchParams.set('calendar-view', isWeekly ? 'week' : 'month')
    window.history.pushState({}, '', url.toString())
  }

  return (
    <div className="w-full p-2 md:p-10">
      <div className="flex justify-between items-center mb-10">
        <h2 className="text-2xl font-medium hidden md:block">
          {format(currentDate, 'MMMM yyyy')}
        </h2>
        <h2 className="font-medium block md:hidden">
          {format(currentDate, 'MMM yyyy')}
        </h2>

        {!isMobile && (
          <div className="flex gap-1">
            <button
              className={`${weeklyView ? 'bg-color-yellow' : ''} px-4 py-1 rounded-md border h-6 text-sm font-medium gap-1 flex justify-center items-center`}
              onClick={() => handleViewChange(true)}
            >
              <Week />
              Week
            </button>
            <button
              className={`${!weeklyView ? 'bg-color-yellow' : ''} px-4 py-1 rounded-md border h-6 text-sm font-medium gap-1 flex justify-center items-center`}
              onClick={() => handleViewChange(false)}
            >
              <Month />
              Month
            </button>
            <PrevNextButton {...handlePrevNextClick()} notDisable={true} />
          </div>
        )}
      </div>

      {weeklyView ? (
        <WeekView
          paramGeners={paramGeners}
          paramLevels={paramLevels}
          paramTypes={paramTypes}
          classCategory={classCategory}
          currentWeek={currentDate}
          category={category}
          selectedType={selectedType}
          selectedGenre={selectedGenre}
          selectedLevel={selectedLevel}
          selfClasses={selfClasses}
          activeTab={activeTab}
        />
      ) : (
        <MonthView
          currentMonth={currentDate}
          calendarRef={calendarRef}
          category={category}
          selectedType={selectedType}
          selectedGenre={selectedGenre}
          selectedLevel={selectedLevel}
          selfClasses={selfClasses}
          activeTab={activeTab}
        />
      )}
    </div>
  )
}

export default CalenderView
