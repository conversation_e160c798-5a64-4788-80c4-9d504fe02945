import FullCalendar from '@fullcalendar/react'
import dayGridPlugin from '@fullcalendar/daygrid'
import { endOfMonth, format, formatISO } from 'date-fns'
import EventCard from './EventCard'
import { startOfMonth } from 'date-fns/fp'
import { useEffect, useState, MutableRefObject, useCallback } from 'react'
import {
  getClassesByDate,
  getClubsByDate,
  getEventsByDate,
} from '@/lib/actions/class.actions'
import { toast } from 'react-toastify'
import { getBackgroundColor } from '@/utils/utils'
import { getTeacherClassesByDate } from '@/lib/actions/teacher.actions'
import { useApi } from '@/hooks/useApi'

interface Event {
  title: string
  start: string
  backgroundColor: string
  textColor: string
  borderColor: string
  extendedProps: any // Add extendedProps to hold full event data
}

interface MonthViewProps {
  currentMonth: Date
  calendarRef: MutableRefObject<FullCalendar | null>
  isEventView?: boolean
  isClubView?: boolean
  category?: 'All' | 'Adult Class' | 'Youth Class'
  selectedType?: string
  selectedGenre?: string[]
  selectedLevel?: string[]
  eventCategory?: string[]
  selfClasses?: boolean
  isTeacherView?: boolean
  activeTab?: string
}

const MonthView: React.FC<MonthViewProps> = ({
  currentMonth,
  calendarRef,
  isEventView = false,
  isClubView = false,
  category = 'All',
  selectedType,
  selectedGenre,
  selectedLevel,
  eventCategory = [],
  selfClasses = false,
  isTeacherView = false,
  activeTab,
}) => {
  const [classData, setClassData] = useState<Event[]>([])
  const [clubResponse, fetchClub] = useApi(
    (start: string, end: string) => getClubsByDate(start, end),
    false,
  )
  const [eventResponse, fetchEvent] = useApi(
    (start: string, end: string, category: string[]) =>
      getEventsByDate(start, end, category),
    false,
  )
  const [teacherClassResponse, fetchTeacherClasses] = useApi(
    (
      accessToken: string,
      start: string,
      end: string,
      category: 'All' | 'Adult Class' | 'Youth Class',
    ) => getTeacherClassesByDate(accessToken, start, end, category),
  )
  const [classResponse, fetchClassData] = useApi(
    (
      accessToken: string,
      start: string,
      end: string,
      category: 'All' | 'Adult Class' | 'Youth Class',
      type?: string,
      genre?: string[],
      level?: string[],
    ) =>
      getClassesByDate(accessToken, start, end, category, type, genre, level),
    selfClasses,
  )

  useEffect(() => {
    if (
      clubResponse.isSuccess ||
      eventResponse.isSuccess ||
      teacherClassResponse.isSuccess ||
      classResponse.isSuccess
    ) {
      const cardData =
        clubResponse?.data?.data ||
        eventResponse?.data?.data ||
        teacherClassResponse?.data?.data ||
        classResponse?.data?.data ||
        []
      const eventObject: Event[] =
        cardData?.map((event: any) => ({
          title: event?.classTitle,
          start: event?.startDate,
          backgroundColor: getBackgroundColor(event?.classLevel),
          textColor: '#000',
          borderColor: '#ffd7a8',
          extendedProps: { ...event },
        })) || []
      setClassData(eventObject)
    }
  }, [clubResponse, eventResponse, teacherClassResponse, classResponse])

  const getClassData = useCallback(
    async (start: string, end: string): Promise<void> => {
      try {
        if (isClubView) {
          await fetchClub(start, end)
        } else if (isEventView) {
          await fetchEvent(start, end, eventCategory)
        } else if (isTeacherView) {
          await fetchTeacherClasses(start, end, 'All')
        } else {
          const classData = [
            start,
            end,
            category,
            selectedType,
            selectedGenre,
            selectedLevel,
          ]
          if (!selfClasses) {
            classData.unshift('')
          }
          await fetchClassData(...classData)
        }
      } catch (error) {
        toast.error('Something went wrong!' + error, { autoClose: 1500 })
      }
    },
    [
      isClubView,
      isEventView,
      JSON.stringify(eventCategory), // Use JSON.stringify for array dependency
      isTeacherView,
      category,
      selfClasses,
      selectedType,
      JSON.stringify(selectedGenre), // Use JSON.stringify for array dependency
      JSON.stringify(selectedLevel), // Use JSON.stringify for array dependency
      // getToken, getBackgroundColor, toast, setClassData are stable or external
    ],
  )

  useEffect(() => {
    const startDate = startOfMonth(currentMonth)
    const formattedStartDate = formatISO(startDate, { representation: 'date' })
    const endDate = endOfMonth(currentMonth)
    const formattedEndDate = formatISO(endDate, { representation: 'date' })
    getClassData(formattedStartDate, formattedEndDate)
  }, [currentMonth, getClassData])

  return (
    <div className="w-full" style={{ backgroundColor: '#FFFFFF' }}>
      <FullCalendar
        ref={calendarRef}
        plugins={[dayGridPlugin]}
        initialView="dayGridMonth"
        events={classData}
        height="auto" // Let the height adjust dynamically
        eventDisplay="block"
        headerToolbar={false}
        displayEventTime={false}
        firstDay={0}
        initialDate={format(currentMonth, 'yyyy-MM-dd')}
        dayMaxEvents={4}
        eventDidMount={(info) => {
          info.el.style.padding = '2px'
          info.el.style.margin = '2px 0'
          info.el.style.whiteSpace = 'normal'
          info.el.style.overflow = 'visible' // Prevent clipping
        }}
        eventContent={(info) => (
          <EventCard classData={info.event.extendedProps} weekView={false} />
        )}
      />
      <style jsx global>{`
        .fc-daygrid-day {
          min-height: 100px;
          vertical-align: top;
        }
        .fc-daygrid-day-frame {
          display: flex;
          flex-direction: column;
          height: 100%;
        }
        .fc-daygrid-day-events {
          flex-grow: 1;
          overflow-y: auto;
          padding-left: 10px;
        }
        .fc .fc-daygrid-event {
          font-size: 0.75rem; // Changed from 0.85rem (equivalent to text-xs)
          padding: 2px 4px; // Added horizontal padding
          margin: 2px 4px; // Added horizontal margin
          width: calc(100% - 8px); // Reduced width to create gaps
          box-sizing: border-box;
          font-weight: normal; // Changed from implicit medium
        }
        .fc-daygrid-day-number {
          z-index: 1 !important;
          padding: 4px;
        }
        .fc-daygrid-day-bottom {
          font-weight: 500;
          font-size: 0.8rem;
          margin-top: 4px;
        }

        @media (max-width: 768px) {
          .fc-daygrid-day {
            min-height: 80px;
          }
          .fc .fc-daygrid-event {
            font-size: 0.65rem; // Adjusted for smaller screens
            padding: 1px 3px;
            margin: 1px 3px;
            width: calc(100% - 6px);
          }
        }

        @media (max-width: 480px) {
          .fc-daygrid-day {
            min-height: 60px;
          }
          .fc .fc-daygrid-event {
            font-size: 0.6rem; // Adjusted for very small screens
            padding: 1px 2px;
            margin: 1px 2px;
            width: calc(100% - 4px);
          }
        }
      `}</style>
    </div>
  )
}

export default MonthView
