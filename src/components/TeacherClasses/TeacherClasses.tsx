'use client'
import Link from 'next/link'
import React, { useEffect, useRef, useState } from 'react'
import ClassCard from '../ClassCard/ClassCard'
import { getClasses } from '@/lib/actions/class.actions'
import { toast } from 'react-toastify'
import ClassGridSkeleton from '../Skeleton/ClassGridSkeleton'
import InfiniteScroll from 'react-infinite-scroll-component'
import { PAGINATION_LIMIT } from '@/utils/constants'
import { useApi } from '@/hooks/useApi'

const TeacherClasses = ({ teacherId }: { teacherId: string }) => {
  const [classData, setClassData] = useState<any>([])
  const [currentClasses, setCurrentClasses] = useState<
    'All' | 'Previous' | 'Current'
  >('Current')
  const [classResponse, fetchClassData] = useApi(
    (
      accessToken: string,
      classType: 'All' | 'Previous' | 'Current',
      classCategory: 'All' | 'Adult Class' | 'Youth Class',
      searchQuery?: string,
      type?: string,
      genre?: string[],
      level?: string[],
      teacherId?: string,
      limit = 20,
      offset = 0,
    ) =>
      getClasses(
        accessToken,
        classType,
        classCategory,
        searchQuery,
        type,
        genre,
        level,
        teacherId,
        limit,
        offset,
      ),
  )
  const [hasMore, setHasMore] = useState<boolean>(true)
  const offset = useRef<number>(0)

  useEffect(() => {
    if (classResponse.isSuccess) {
      const moreClasses = classResponse?.data?.data?.data || []
      if (moreClasses.length > 0) {
        setClassData((prev: any) => [...prev, ...moreClasses])
        offset.current += PAGINATION_LIMIT
        if (
          classResponse?.data?.data?.total <=
          classData.length + moreClasses.length
        ) {
          setHasMore(false)
        }
      } else {
        setHasMore(false)
      }
    }
  }, [classResponse])

  useEffect(() => {
    setClassData([])
    setHasMore(true)
    offset.current = 0
  }, [currentClasses])

  useEffect(() => {
    if (hasMore) {
      fetchMoreClass()
    }
  }, [hasMore])

  const fetchMoreClass = async () => {
    try {
      await fetchClassData(
        currentClasses,
        'All',
        undefined,
        undefined,
        undefined,
        undefined,
        teacherId,
        PAGINATION_LIMIT,
        offset.current,
      )
    } catch (error) {
      toast.error('Something went wrong!' + error, { autoClose: 1500 })
    }
  }

  return (
    <div>
      <div className="w-full px-5 flex items-center gap-4">
        <button
          className={`h-8 px-3 py-2 rounded-lg border ${currentClasses == 'Current' ? 'bg-color-yellow' : ''} text-xs font-bold`}
          onClick={() => setCurrentClasses('Current')}
        >
          Current classes
        </button>
        <button
          className={`h-8 px-3 py-2 rounded-lg border ${currentClasses == 'Previous' ? 'bg-color-yellow' : ''} text-xs font-bold`}
          onClick={() => setCurrentClasses('Previous')}
        >
          Previous classes
        </button>
      </div>
      <InfiniteScroll
        dataLength={classData.length}
        next={fetchMoreClass}
        hasMore={hasMore}
        loader={<ClassGridSkeleton />}
        endMessage={
          <div className="flex items-center font-bold justify-center mb-10 text-color-grey-1">
            No more classes to show
          </div>
        }
        scrollableTarget="scrollableDiv"
      >
        <div className="p-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {classData.map((item: any, index: number) => {
            return (
              <Link href={'/classes/' + item?.Id} key={index}>
                <ClassCard
                  data={item}
                  showRating={false}
                  showDetails={true}
                  showTeachers={false}
                  showProgress={index == 0 || index == 8}
                  shadow={'sm'}
                />
              </Link>
            )
          })}
        </div>
      </InfiniteScroll>
    </div>
  )
}

export default TeacherClasses
