'use client'
import React, { useCallback, useEffect, useState } from 'react'
import {
  CountrySelect,
  StateSelect,
  CitySelect,
} from 'react-country-state-city'
import 'react-country-state-city/dist/react-country-state-city.css'
import EditCartIcon from '../Icons/EditCartIcon'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useApi } from '@/hooks/useApi'
import { updateAccountById } from '@/lib/actions/account.actions'
import { toast } from 'react-toastify'

interface Address {
  street: string
  address2?: string
  city: string
  state: string
  country: string
  postalCode: string
}

interface AddressCardProps {
  userData: any
}

const AddressCard = ({ userData }: AddressCardProps) => {
  console.log('userData', userData)
  const [editMode, setEditMode] = useState<'billing' | 'shipping' | null>(null)
  const [editData, setEditData] = useState<Address>({
    street: '',
    address2: '',
    city: '',
    state: '',
    country: '',
    postalCode: '',
  })
  const [billingAddress, setBillingAddress] = useState<Address>(
    userData.BillingAddress || {
      street: '',
      address2: '',
      city: '',
      state: '',
      country: '',
      postalCode: '',
    },
  )
  const [shippingAddress, setShippingAddress] = useState<Address>(
    userData.ShippingAddress || {
      street: '',
      address2: '',
      city: '',
      state: '',
      country: '',
      postalCode: '',
    },
  )
  const [selectedCountry, setSelectedCountry] = useState<any>(null)
  const [selectedState, setSelectedState] = useState<any>(null)
  const [selectedCity, setSelectedCity] = useState<any>(null)
  const [response, updateDetails] = useApi((access: string, data: any) =>
    updateAccountById(access, data),
  )

  useEffect(() => {
    if (response.isSuccess) {
      if (editMode === 'billing') {
        setBillingAddress(editData)
      } else if (editMode === 'shipping') {
        setShippingAddress(editData)
      }
      setEditMode(null)
      setEditData({
        street: '',
        address2: '',
        city: '',
        state: '',
        country: '',
        postalCode: '',
      })
      setSelectedCountry(null)
      setSelectedState(null)
      setSelectedCity(null)
      toast.success('Address updated successfully!')
    }
  }, [response])

  const handleEdit = useCallback(
    (type: 'billing' | 'shipping') => {
      const address = type === 'billing' ? billingAddress : shippingAddress
      setEditData(address)
      setEditMode(type)

      // Handle country selection with null checks
      if (address?.country) {
        setSelectedCountry({ name: address.country, id: address.country })
      } else {
        setSelectedCountry(null)
      }

      // Handle state selection with null checks
      if (address?.state) {
        setSelectedState({ name: address.state, id: address.state })
      } else {
        setSelectedState(null)
      }

      // Handle city selection with null checks
      if (address?.city) {
        setSelectedCity({ name: address.city, id: address.city })
      } else {
        setSelectedCity(null)
      }
    },
    [billingAddress, shippingAddress],
  )

  const handleSave = () => {
    let updateData: any = {}
    if (editMode === 'billing') {
      updateData = {
        billingStreet: editData.street,
        billingCity: editData.city,
        billingState: editData.state,
        billingCountry: editData.country,
        billingPostalCode: editData.postalCode,
      }
      setBillingAddress(editData)
    } else {
      updateData = {
        shippingStreet: editData.street,
        shippingCity: editData.city,
        shippingState: editData.state,
        shippingCountry: editData.country,
        shippingPostalCode: editData.postalCode,
      }
      setShippingAddress(editData)
    }
    updateDetails(updateData)
  }

  const handleCancel = () => {
    setEditMode(null)
    setEditData({
      street: '',
      address2: '',
      city: '',
      state: '',
      country: '',
      postalCode: '',
    })
    setSelectedCountry(null)
    setSelectedState(null)
    setSelectedCity(null)
  }

  const handleInputChange = (field: keyof Address, value: string) => {
    setEditData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const renderAddressDisplay = (address: Address) => {
    // Check if address exists and has data
    if (
      !address ||
      (!address.street && !address.city && !address.state && !address.country)
    ) {
      return (
        <div className="text-sm text-gray-500 italic">
          No address information available
        </div>
      )
    }

    return (
      <div className="text-sm font-medium">
        {address?.street && <div>{address.street}</div>}
        {address?.address2 && <div>{address.address2}</div>}
        {(address?.city || address?.postalCode) && (
          <div>
            {[address?.city, address?.postalCode].filter(Boolean).join(' ')}
          </div>
        )}
        {(address?.state || address?.country) && (
          <div>
            {[address?.state, address?.country].filter(Boolean).join(', ')}
          </div>
        )}
      </div>
    )
  }

  const renderAddressForm = () => (
    <div className="flex flex-col gap-3">
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Street Address
        </label>
        <input
          type="text"
          value={editData?.street || ''}
          onChange={(e) => handleInputChange('street', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter street address"
        />
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Country
          </label>
          <CountrySelect
            onChange={(country: any) => {
              setSelectedCountry(country)
              setSelectedState(null)
              setSelectedCity(null)
              setEditData((prev) => ({
                ...prev,
                country: country?.name || '',
                state: '',
                city: '',
              }))
            }}
            placeHolder="Select Country"
            defaultValue={selectedCountry}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            State
          </label>
          <StateSelect
            countryid={selectedCountry?.id}
            onChange={(state: any) => {
              setSelectedState(state)
              setSelectedCity(null)
              setEditData((prev) => ({
                ...prev,
                state: state?.name || '',
                city: '', // Reset city when state changes
              }))
            }}
            placeHolder="Select State"
            defaultValue={selectedState}
            disabled={!selectedCountry}
            className="w-full"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            City
          </label>
          <CitySelect
            countryid={selectedCountry?.id}
            stateid={selectedState?.id}
            onChange={(city: any) => {
              setSelectedCity(city)
              setEditData((prev) => ({ ...prev, city: city?.name || '' }))
            }}
            placeHolder="Select City"
            defaultValue={selectedCity}
            disabled={!selectedState}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Postal Code
          </label>
          <input
            type="text"
            value={editData?.postalCode || ''}
            onChange={(e) => handleInputChange('postalCode', e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Postal Code"
          />
        </div>
      </div>

      <div className="flex gap-3 mt-4">
        <CustomButton
          onClick={handleSave}
          isLoading={response.isFetching}
          title="Save"
          width="w-max"
        />
        <button
          onClick={handleCancel}
          className="px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md transition-colors"
        >
          Cancel
        </button>
      </div>
    </div>
  )

  return (
    <div className="w-full bg-white rounded-2xl flex flex-col gap-5">
      <h1 className="text-sm font-semibold">
        The following addresses will be used on the checkout page by default.
      </h1>

      {/* Billing Address Section */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between gap-5">
          <h1 className="text-lg font-bold">Billing Address</h1>
          <button
            onClick={() => handleEdit('billing')}
            className="text-sm font-semibold flex items-center gap-2 text-color-blue-1 hover:opacity-70 transition-opacity"
            disabled={editMode !== null}
          >
            Edit <EditCartIcon />
          </button>
        </div>

        <div className="w-full border bg-color-grey-2"></div>

        {editMode === 'billing'
          ? renderAddressForm()
          : renderAddressDisplay(billingAddress)}
      </div>

      {/* Shipping Address Section */}
      <div className="flex flex-col gap-4 mt-6">
        <div className="flex items-center justify-between gap-5">
          <h1 className="text-lg font-bold">Shipping Address</h1>
          <button
            onClick={() => handleEdit('shipping')}
            className="text-sm font-semibold flex items-center gap-2 text-color-blue-1 hover:opacity-70 transition-opacity"
            disabled={editMode !== null}
          >
            Edit <EditCartIcon />
          </button>
        </div>

        <div className="w-full border bg-color-grey-2"></div>

        {editMode === 'shipping'
          ? renderAddressForm()
          : renderAddressDisplay(shippingAddress)}
      </div>
    </div>
  )
}

export default AddressCard
