import React, { useEffect, useMemo } from 'react'
import { useApi } from '@/hooks/useApi'
import { purchaseMembership } from '@/lib/actions/membership.actions'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { capitalizeFirstLetter } from '@/utils/utils'
import { useDispatch } from 'react-redux'
import { setPaymentToken } from '@/lib/redux'
import { useRouter } from 'next/navigation'

const MembershipCard = ({
  plan,
  handleModal,
  billing,
}: {
  plan: any
  handleModal: () => void
  billing: any
}) => {
  const [purchaseResponse, chooseMembership] = useApi(
    (access: string, productId: string, type: 'Monthly' | 'Annual') =>
      purchaseMembership(access, productId, type),
  )
  const isCurrent = plan.current
  const isnext = plan?.isNext
  const dispatch = useDispatch()
  const router = useRouter()

  useEffect(() => {
    if (purchaseResponse.isSuccess) {
      dispatch(setPaymentToken(purchaseResponse.data?.data?.token))
      router.push(`/checkout?token=${purchaseResponse.data?.data?.token}`)
    }
  }, [purchaseResponse])

  const period = useMemo(() => {
    return capitalizeFirstLetter(billing)
  }, [billing])
  return (
    <div
      className={`rounded-2xl p-8 ${isnext ? 'bg-black text-white shadow-lg' : 'bg-white border'} ${isnext ? '' : 'shadow-sm'} ${isCurrent ? 'border-2 border-gray-200' : ''}`}
      style={{ marginBottom: '0.5rem' }}
    >
      <div className="grid grid-cols-1 md:grid-cols-12 items-center gap-4">
        {/* Info */}
        <div className="mb-4 md:mb-0 md:col-span-7">
          <div
            className={`font-bold text-2xl mb-1 ${isnext ? 'text-white' : 'text-black'}`}
          >
            {plan.name}
          </div>
          <div
            className={`text-base mb-2 whitespace-pre-line ${isnext ? 'text-white/80' : 'text-gray-500'}`}
          >
            {plan.description}
          </div>
          <a
            href="#"
            className={`text-xs underline ${isnext ? 'text-white/80' : 'text-gray-500'}`}
          >
            View All Benefits
          </a>
        </div>
        {/* Price */}
        <div className="flex justify-end items-center mb-4 md:mb-0 md:col-span-2">
          <div
            className={`font-bold text-2xl ${isnext ? 'text-white' : 'text-black'}`}
          >
            ${plan.price[billing]}/{billing === 'monthly' ? 'mo' : 'yr'}
          </div>
        </div>
        {/* Button */}
        <div className="flex flex-col items-center justify-center gap-2 md:col-span-3">
          {isCurrent ? (
            <div className="bg-gray-100 text-gray-400 font-semibold px-6 py-3 rounded-lg cursor-pointer w-40">
              Current Plan
            </div>
          ) : (
            <CustomButton
              title="Choose Plan"
              onClick={() => chooseMembership(plan.id, period)}
              isLoading={purchaseResponse.isFetching}
              height={12}
              width="w-40"
            />
          )}
          {plan.current && (
            <button
              className="text-xs underline text-gray-400 hover:text-black transition"
              onClick={handleModal}
            >
              Manage Plan
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default MembershipCard
