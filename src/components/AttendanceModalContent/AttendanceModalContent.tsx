'use client'
import { useApi } from '@/hooks/useApi'
import {
  getMeetingManualAttendance,
  markManualAttendance,
} from '@/lib/actions/teacher.actions'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import AttendanceModalSkeleton from '../Skeletons/AttendanceModalSkeleton'
import { InfoIcon } from '../Icons/InfoIcon'
import { X } from 'lucide-react'
import { setTodoList, useDispatch } from '@/lib/redux'

type AttendanceModalProps = {
  meetingId: string
  onClose: () => void
  setTodoListItems?: any
  isNotesRequired?: boolean
}

export default function AttendanceModalContent({
  meetingId,
  onClose,
  setTodoListItems,
  isNotesRequired,
}: AttendanceModalProps) {
  const [formData, setFormData] = useState({
    adultInPerson: '',
    youthInPerson: '',
    adultOnline: '',
    youthOnline: '',
    paidTeacherInPerson: '',
    paidTeacherOnline: '',
    unpaidTeacherInPerson: '',
    unpaidTeacherOnline: '',
    attendanceNotes: '',
    subjectOfClass: '',
    practicalApplications: '',
  })
  const [notesError, setNotesError] = useState<string>('')
  const [getAttendanceResponse, getAttendance] = useApi((access, meetingId) =>
    getMeetingManualAttendance(access, meetingId),
  )
  console.log('isNotesRequired', isNotesRequired)
  const [response, markAttendance] = useApi(
    (
      access,
      meetingId,
      adultInPerson,
      youthInPerson,
      adultOnline,
      youthOnline,
      paidTeacherInPerson,
      paidTeacherOnline,
      unpaidTeacherInPerson,
      unpaidTeacherOnline,
      attendanceNotes,
      subjectOfClass,
      practicalApplications,
    ) =>
      markManualAttendance(
        access,
        meetingId,
        adultInPerson,
        youthInPerson,
        adultOnline,
        youthOnline,
        paidTeacherInPerson,
        paidTeacherOnline,
        unpaidTeacherInPerson,
        unpaidTeacherOnline,
        attendanceNotes,
        subjectOfClass,
        practicalApplications,
      ),
  )

  const dispatch = useDispatch()

  useEffect(() => {
    getAttendance(meetingId)
  }, [])

  useEffect(() => {
    if (getAttendanceResponse.isSuccess) {
      const attendanceData = getAttendanceResponse?.data?.data[0]
      setFormData({
        adultInPerson: String(attendanceData?.present_in_person_adult ?? ''),
        youthInPerson: String(attendanceData?.present_in_person_youth ?? ''),
        adultOnline: String(attendanceData?.present_online_adult ?? ''),
        youthOnline: String(attendanceData?.present_online_youth ?? ''),
        paidTeacherInPerson: String(
          attendanceData?.present_in_person_paid_teacher ?? '',
        ),
        paidTeacherOnline: String(
          attendanceData?.present_online_paid_teacher ?? '',
        ),
        unpaidTeacherInPerson: String(
          attendanceData?.present_in_person_unpaid_teacher ?? '',
        ),
        unpaidTeacherOnline: String(
          attendanceData?.present_online_unpaid_teacher ?? '',
        ),
        attendanceNotes: attendanceData?.attendance_notes ?? '',
        subjectOfClass: attendanceData?.subject_of_class ?? '',
        practicalApplications: attendanceData?.practical_applications ?? '',
      })
    }
  }, [getAttendanceResponse])

  useEffect(() => {
    if (response.isSuccess) {
      if (setTodoListItems) {
        setTodoListItems((prev: any) => {
          const updated = prev.filter((item: any) => item.Id !== meetingId)
          dispatch(setTodoList(updated))
          return updated
        })
      }
      onClose()
      toast.success('Attendance marked successfully!')
      setFormData({
        adultInPerson: '',
        youthInPerson: '',
        adultOnline: '',
        youthOnline: '',
        paidTeacherInPerson: '',
        paidTeacherOnline: '',
        unpaidTeacherInPerson: '',
        unpaidTeacherOnline: '',
        attendanceNotes: '',
        subjectOfClass: '',
        practicalApplications: '',
      })
    }
  }, [response])

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target

    // Clear notes error when user starts typing in attendanceNotes field
    if (name === 'attendanceNotes' && notesError) {
      setNotesError('')
    }

    setFormData({
      ...formData,
      [name]:
        name.includes('adult') ||
        name.includes('youth') ||
        name.includes('Teacher')
          ? parseInt(value) || ''
          : value,
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Clear previous error
    setNotesError('')

    // Validate notes if required
    if (
      isNotesRequired &&
      (!formData.attendanceNotes || formData.attendanceNotes.trim() === '')
    ) {
      setNotesError('Notes or concerns field is required')
      toast.error('Please fill in the required notes or concerns field', {
        autoClose: 2000,
      })
      return
    }

    markAttendance(
      meetingId,
      parseInt(formData.adultInPerson) || 0,
      parseInt(formData.youthInPerson) || 0,
      parseInt(formData.adultOnline) || 0,
      parseInt(formData.youthOnline) || 0,
      parseInt(formData.paidTeacherInPerson) || 0,
      parseInt(formData.paidTeacherOnline) || 0,
      parseInt(formData.unpaidTeacherInPerson) || 0,
      parseInt(formData.unpaidTeacherOnline) || 0,
      formData.attendanceNotes,
      formData.subjectOfClass,
      formData.practicalApplications,
    )
  }

  return (
    <div className="bg-white rounded-lg px-6 py-4 max-h-[90vh] overflow-y-auto">
      <div className="flex items-center justify-between border-b-2 pb-2 sticky top-0 bg-white z-10">
        <h2 className="text-xl font-bold text-gray-800">Mark Attendance</h2>
        <button
          onClick={onClose}
          className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
        >
          <X size={20} className="text-color-grey-1" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="mt-4">
        {getAttendanceResponse.isFetching ? (
          <AttendanceModalSkeleton />
        ) : (
          <div className="space-y-6">
            <table className="w-full border-collapse">
              <thead>
                <tr>
                  <th className="border p-2 text-left text-sm font-bold">
                    Category
                  </th>
                  <th className="border p-2 text-left text-sm font-bold">
                    In-Person
                  </th>
                  <th className="border p-2 text-left text-sm font-bold">
                    Online
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border p-2 text-sm font-medium flex items-center gap-2">
                    <span>Adults</span>
                    <InfoIcon tooltipText="Number of adult attendees (18+ years old, excluding teachers/hosts/performers)." />
                  </td>
                  <td className="border p-2">
                    <input
                      type="number"
                      id="adultInPerson"
                      name="adultInPerson"
                      value={formData.adultInPerson}
                      onChange={handleChange}
                      className="w-full px-2 py-1 rounded-md border shadow-sm sm:text-sm"
                    />
                  </td>
                  <td className="border p-2">
                    <input
                      type="number"
                      id="adultOnline"
                      name="adultOnline"
                      value={formData.adultOnline}
                      onChange={handleChange}
                      className="w-full px-2 py-1 rounded-md border shadow-sm sm:text-sm"
                    />
                  </td>
                </tr>
                <tr>
                  <td className="border p-2 text-sm font-medium flex items-center gap-2">
                    <span>Youth</span>
                    <InfoIcon tooltipText="Number of youth attendees (under 18 years old, excluding teachers/hosts/performers)." />
                  </td>
                  <td className="border p-2">
                    <input
                      type="number"
                      id="youthInPerson"
                      name="youthInPerson"
                      value={formData.youthInPerson}
                      onChange={handleChange}
                      className="w-full px-2 py-1 rounded-md border shadow-sm sm:text-sm"
                    />
                  </td>
                  <td className="border p-2">
                    <input
                      type="number"
                      id="youthOnline"
                      name="youthOnline"
                      value={formData.youthOnline}
                      onChange={handleChange}
                      className="w-full px-2 py-1 rounded-md border shadow-sm sm:text-sm"
                    />
                  </td>
                </tr>
                <tr>
                  <td className="border p-2 text-sm font-medium flex items-center gap-2">
                    <span>Paid Teachers/Hosts/Performers</span>
                    <InfoIcon tooltipText="Number of compensated teachers, hosts, or performers leading or facilitating the meeting." />
                  </td>
                  <td className="border p-2">
                    <input
                      type="number"
                      id="paidTeacherInPerson"
                      name="paidTeacherInPerson"
                      value={formData.paidTeacherInPerson}
                      onChange={handleChange}
                      className="w-full px-2 py-1 rounded-md border shadow-sm sm:text-sm"
                    />
                  </td>
                  <td className="border p-2">
                    <input
                      type="number"
                      id="paidTeacherOnline"
                      name="paidTeacherOnline"
                      value={formData.paidTeacherOnline}
                      onChange={handleChange}
                      className="w-full px-2 py-1 rounded-md border shadow-sm sm:text-sm"
                    />
                  </td>
                </tr>
                <tr>
                  <td className="border p-2 text-sm font-medium flex items-center gap-2">
                    <span>Unpaid Teachers/Hosts/Performers</span>
                    <InfoIcon tooltipText="Number of volunteer teachers, hosts, or performers leading or facilitating the meeting without compensation." />
                  </td>
                  <td className="border p-2">
                    <input
                      type="number"
                      id="unpaidTeacherInPerson"
                      name="unpaidTeacherInPerson"
                      value={formData.unpaidTeacherInPerson}
                      onChange={handleChange}
                      className="w-full px-2 py-1 rounded-md border shadow-sm sm:text-sm"
                    />
                  </td>
                  <td className="border p-2">
                    <input
                      type="number"
                      id="unpaidTeacherOnline"
                      name="unpaidTeacherOnline"
                      value={formData.unpaidTeacherOnline}
                      onChange={handleChange}
                      className="w-full px-2 py-1 rounded-md border shadow-sm sm:text-sm"
                    />
                  </td>
                </tr>
              </tbody>
            </table>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="subjectOfClass"
                  className="block text-sm font-bold text-gray-700 mb-2"
                >
                  Subject of Class
                </label>
                <textarea
                  id="subjectOfClass"
                  name="subjectOfClass"
                  rows={3}
                  value={formData.subjectOfClass}
                  onChange={handleChange}
                  className="block w-full px-3 py-2 rounded-md border border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm"
                  placeholder="What topics or subjects were covered in this class?"
                />
              </div>

              <div>
                <label
                  htmlFor="practicalApplications"
                  className="block text-sm font-bold text-gray-700 mb-2"
                >
                  Practical Applications
                </label>
                <textarea
                  id="practicalApplications"
                  name="practicalApplications"
                  rows={3}
                  value={formData.practicalApplications}
                  onChange={handleChange}
                  className="block w-full px-3 py-2 rounded-md border border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm"
                  placeholder="What practical skills or applications were taught?"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="attendanceNotes"
                className="block text-sm font-bold text-gray-700 mb-2"
              >
                Notes or Concerns
                {isNotesRequired && (
                  <span className="text-red-500 ml-1">*</span>
                )}
              </label>
              <textarea
                id="attendanceNotes"
                name="attendanceNotes"
                rows={3}
                value={formData.attendanceNotes}
                onChange={handleChange}
                className={`block w-full px-3 py-2 rounded-md border shadow-sm focus:ring-gray-500 sm:text-sm ${
                  notesError
                    ? 'border-red-500 focus:border-red-500'
                    : 'border-gray-300 focus:border-gray-500'
                }`}
                placeholder="Any additional notes or concerns about attendance..."
              />
              {notesError && (
                <p className="text-red-500 text-xs mt-1">{notesError}</p>
              )}
            </div>
          </div>
        )}

        <div className="mt-6 flex justify-end space-x-3 sticky bottom-0 bg-white pt-4 border-t">
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={response.isFetching}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {response.isFetching ? 'Saving...' : 'Save Attendance'}
          </button>
        </div>
      </form>
    </div>
  )
}
