import React, { memo, useCallback, useEffect, useRef, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import ClassCard from '../ClassCard/ClassCard'
import Link from 'next/link'
import Loader from '../Loader/Loader'
import { getClasses } from '@/lib/actions/class.actions'
import { toast } from 'react-toastify'
import { PAGINATION_LIMIT } from '@/utils/constants'
import { useApi } from '@/hooks/useApi'
import ClassGridSkeleton from '../Skeleton/ClassGridSkeleton'

interface ListClassProps {
  search: string
  paramGeners: string | null
  paramLevels: string | null
  paramTypes: string | null
  teacherId: string | null
  classCategory: string | null
  category: 'All' | 'Adult Class' | 'Youth Class'
  selectedType: string
  selectedGenre: string[]
  selectedLevel: string[]
  selectedTeacher: string
  selfClasses?: boolean
  activeTab?: string
  classType?: string | null
}

const ListClass = ({
  search,
  paramGeners,
  paramLevels,
  paramTypes,
  teacherId,
  classCategory,
  category,
  selectedType,
  selectedGenre,
  selectedLevel,
  selectedTeacher,
  selfClasses,
  activeTab,
  classType,
}: ListClassProps) => {
  const [hasMore, setHasMore] = useState<boolean>(true)
  const offset = useRef<number>(0)
  const [classesData, setClassesData] = useState<any[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [classResponse, fetchClassData] = useApi(
    (
      accessToken: string,
      classType: 'All' | 'Previous' | 'Current',
      classCategory: 'All' | 'Adult Class' | 'Youth Class',
      searchQuery?: string,
      type?: string,
      genre?: string[],
      level?: string[],
      teacherId?: string,
      limit = 20,
      offset = 0,
    ) =>
      getClasses(
        accessToken,
        classType,
        classCategory,
        searchQuery,
        type,
        genre,
        level,
        teacherId,
        limit,
        offset,
      ),
    selfClasses,
  )

  useEffect(() => {
    if (classResponse.isSuccess) {
      const moreClasses = classResponse?.data?.data?.data || []
      if (moreClasses.length > 0) {
        setClassesData((prev: any) => [...prev, ...moreClasses])
        offset.current += PAGINATION_LIMIT
        if (
          classResponse?.data?.data?.total <=
          classesData.length + moreClasses.length
        ) {
          setHasMore(false)
        }
      } else {
        setHasMore(false)
      }
    }
  }, [classResponse])

  const fetchMoreData = useCallback(async () => {
    if (!hasMore) return
    setLoading(true)
    const classData = [
      classType ? classType : selfClasses ? activeTab : 'Current',
      category,
      search,
      selectedType,
      selectedGenre,
      selectedLevel,
      selectedTeacher,
      PAGINATION_LIMIT,
      offset.current,
    ]
    if (!selfClasses) {
      classData.unshift('')
    }
    try {
      await fetchClassData(...classData)
    } catch (error) {
      toast.error('Something went wrong! ' + error, { autoClose: 1500 })
    } finally {
      setLoading(false)
    }
  }, [
    category,
    search,
    selectedType,
    selectedGenre,
    selectedLevel,
    selectedTeacher,
    classesData,
    activeTab,
    classType,
  ])

  const fetchInitialData = useCallback(async () => {
    setLoading(true)
    const classData = [
      classType ? classType : selfClasses ? activeTab : 'Current',
      category,
      search,
      selectedType,
      selectedGenre,
      selectedLevel,
      selectedTeacher,
      PAGINATION_LIMIT,
      0,
    ]
    if (!selfClasses) {
      classData.unshift('')
    }
    try {
      await fetchClassData(...classData)
    } catch (error) {
      toast.error('Something went wrong! ' + error, { autoClose: 1500 })
    } finally {
      setLoading(false)
    }
  }, [
    category,
    search,
    selectedType,
    selectedGenre,
    selectedLevel,
    selectedTeacher,
    activeTab,
    classType,
  ])

  useEffect(() => {
    const resetAndFetch = async () => {
      setLoading(true)
      setClassesData([])
      offset.current = 0
      setHasMore(true)
    }
    resetAndFetch()
  }, [
    search,
    paramGeners,
    paramLevels,
    paramTypes,
    teacherId,
    classCategory,
    category,
    selectedType,
    JSON.stringify(selectedGenre),
    JSON.stringify(selectedLevel),
    selectedTeacher,
    activeTab,
  ])

  useEffect(() => {
    if (hasMore) {
      ;(async () => {
        await fetchInitialData()
      })()
    }
  }, [hasMore])

  // Show skeleton during initial load
  if (loading && classesData.length === 0) {
    return <ClassGridSkeleton count={6} />
  }

  return (
    <>
      {!loading && classesData.length === 0 ? (
        <div className="flex items-center font-bold justify-center my-10 text-color-grey-1">
          No results found
        </div>
      ) : (
        <InfiniteScroll
          dataLength={classesData.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={
            <div className="w-full flex my-10 items-center justify-center">
              <Loader />
            </div>
          }
          endMessage={
            <div className="flex items-center font-bold justify-center mb-10 text-color-grey-1">
              No more classes to show
            </div>
          }
          scrollableTarget="scrollableDiv"
        >
          {classesData.length > 0 && (
            <div className="p-3 md:p-7 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {classesData.map((item: any) => (
                <Link href={`/classes/${item?.Id}`} key={item?.Id}>
                  <ClassCard
                    data={item}
                    showRating={false}
                    showDetails={true}
                    shadow={'sm'}
                  />
                </Link>
              ))}
            </div>
          )}
        </InfiniteScroll>
      )}
    </>
  )
}

export default memo(ListClass)
