'use client'
import { useEffect, useMemo, useState } from 'react'
import CustomDropDown from '../CustomComponents/CustomDropDown/CustomDropDown'
import FilterIcon from '../Icons/FilterIcon'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { makeAnonymousDonation, makeDonation } from '@/lib/actions/cart.actions'
import { useRouter } from 'next/navigation'
import DonationInfo from '../DonationInfo/DonationInfo'
import FlowerIcon from '../Icons/FlowerIcon'
import { PaymentType } from '@/utils/constants'
import { useApi } from '@/hooks/useApi'
import { useDispatch } from 'react-redux'
import { setPaymentToken } from '@/lib/redux'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import EmailVerify from '../EmailVerify/EmailVerify'
import CustomTooltip from '../CustomComponents/CustomTooltip/CustomTooltip'

const presetAmounts = [25, 50, 100, 200, 300, 500, 1000]

interface DonationPageProps {
  donationFundTypes: { id: string; name: string }[]
}

const DonationPage = ({ donationFundTypes }: DonationPageProps) => {
  const [fundType, setFundType] = useState<string>('')
  const [amount, setAmount] = useState<number>(0)
  const [filterTitle, setFilterTitle] = useState<string>('Choose an option')
  const [isDisabled, setIsDisabled] = useState<boolean>(false)
  const [donationType, setDonationType] = useState<PaymentType>('One-time')
  const [showCustomAmount, setShowCustomAmount] = useState<boolean>(false)
  const [isAnonymous, setIsAnonymous] = useState<boolean>(false)

  // New state for non-logged-in users
  const [userInfo, setUserInfo] = useState({
    first_name: '',
    last_name: '',
    email: '',
  })
  const [email, setEmail] = useState<string>('')
  const [phoneNumber, setPhoneNumber] = useState<string>('')
  const [isEmailVerified, setIsEmailVerified] = useState<boolean>(false)

  const dispatch = useDispatch()
  const router = useRouter()
  const [donationResponse, makeDonaton] = useApi((access: string) =>
    makeDonation(access, amount, fundType, donationType),
  )
  const [nonLoggedinDonationResponse, makeNonLoggedinDonation] = useApi(
    (
      firtName: string,
      lastName: string,
      email: string,
      amount: number,
      fundType: string,
      donationType: PaymentType,
      isAnonymous: boolean,
    ) =>
      makeAnonymousDonation(
        firtName,
        lastName,
        email,
        amount,
        fundType,
        donationType,
        isAnonymous,
      ),
    false,
  )

  const { user } = useAuth()

  useEffect(() => {
    if (user) {
      // If user is logged in, check fundType and amount only
      if (fundType && amount) {
        setIsDisabled(false)
      } else {
        setIsDisabled(true)
      }
    } else {
      // If user is not logged in, check fundType, amount, firstName, email, and email verification
      if (
        fundType &&
        amount &&
        userInfo.first_name &&
        userInfo.last_name &&
        userInfo.email &&
        isEmailVerified
      ) {
        setIsDisabled(false)
      } else {
        setIsDisabled(true)
      }
    }
  }, [
    fundType,
    amount,
    userInfo.first_name,
    userInfo.last_name,
    userInfo.email,
    isEmailVerified,
    user,
  ])

  useEffect(() => {
    if (donationResponse.isSuccess) {
      dispatch(setPaymentToken(donationResponse?.data?.data?.token))
      router.push(`/checkout?token=${donationResponse?.data?.data?.token}`)
    }
    if (nonLoggedinDonationResponse.isSuccess) {
      dispatch(setPaymentToken(nonLoggedinDonationResponse?.data?.data?.token))
      router.push(
        `/checkout?token=${nonLoggedinDonationResponse?.data?.data?.token}`,
      )
    }
  }, [donationResponse, nonLoggedinDonationResponse])

  const handleFilter = (value: string, filterType: string) => {
    setFundType(value)
    setFilterTitle(filterType)
  }

  const handleUserInfoChange = (field: string, value: string) => {
    setUserInfo((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleDonation = async () => {
    if (amount <= 0) {
      toast.error('Please enter a valid amount')
      return
    }
    if (user) {
      makeDonaton()
    } else {
      makeNonLoggedinDonation(
        userInfo.first_name,
        userInfo.last_name,
        userInfo.email,
        amount,
        fundType,
        donationType as PaymentType,
        isAnonymous,
      )
    }
  }

  const donationOptions = useMemo(() => {
    return donationFundTypes.map((type) => ({
      value: type.name,
      label: type.name,
    }))
  }, [donationFundTypes])

  return (
    <div className="w-full flex justify-center">
      <div className="w-11/12 md:w-6/12 mt-5">
        <div className="bg-white w-full pt-8 pb-10 px-5 h-max flex flex-col gap-4 rounded-2xl">
          <div className="flex w-full justify-center items-center">
            <FlowerIcon />
          </div>
          <h1 className="text-2xl font-bold text-center">Donate to the Muse</h1>
          <p className="font-medium text-sm text-center">
            Help writers find their voice and share their stories. Your donation
            directly funds scholarships, workshops, and programs that empower
            writers to grow, connect, and succeed.
          </p>
          <div className="flex flex-col gap-4">
            {!user && (
              <div className="w-full">
                <h2 className="text-xl font-bold mb-4">ACKNOWLEDGEMENTS</h2>

                <div className="flex flex-col gap-4">
                  <div className="flex flex-col gap-2">
                    <div className="grid grid-cols-2 gap-5">
                      <div className="flex flex-col gap-2">
                        <label className="block text-gray-700 text-sm font-semibold">
                          First Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          value={userInfo.first_name}
                          onChange={(e) =>
                            handleUserInfoChange('first_name', e.target.value)
                          }
                          className="w-full h-12 p-2 border border-gray-200 rounded-lg text-sm"
                          placeholder="Enter first name"
                          required
                        />
                      </div>
                      <div className="flex flex-col gap-2">
                        <label className="block text-gray-700 text-sm font-semibold">
                          Last Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          value={userInfo.last_name}
                          onChange={(e) =>
                            handleUserInfoChange('last_name', e.target.value)
                          }
                          className="w-full h-12 p-2 border border-gray-200 rounded-lg text-sm"
                          placeholder="Enter last name"
                          required
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <label className="text-gray-700 text-sm font-semibold flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={isAnonymous}
                          onChange={(e) => setIsAnonymous(e.target.checked)}
                          className="h-4 w-4"
                        />
                        <span>Anonymous</span>
                      </label>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <EmailVerify
                      user={null}
                      email={email}
                      setEmail={(email: any) => {
                        setEmail(email)
                        handleUserInfoChange('email', email)
                      }}
                      phoneNumber={phoneNumber}
                      studentOnBoarding={false}
                      setIsEmailVerified={setIsEmailVerified}
                      isEmailVerified={isEmailVerified}
                      title="Email"
                    />
                  </div>
                </div>
              </div>
            )}
            <div className="flex flex-col gap-2 w-full">
              <label className="block text-gray-700 text-sm font-semibold self-start mt-2">
                Donation Fund Type:
              </label>
              <CustomDropDown
                icon={<FilterIcon />}
                title={filterTitle}
                options={donationOptions}
                handleOptionClick={handleFilter}
                column={1}
                width="w-full"
              />
            </div>

            <div className="flex flex-col gap-2">
              <label className="block text-gray-700 text-sm font-semibold">
                Choose how you want to donate:
              </label>
              <div className="flex gap-4 text-sm font-semibold">
                <button
                  onClick={() => setDonationType('One-time')}
                  className={`px-2 md:px-4 py-1 md:py-3 rounded-lg ${
                    donationType === 'One-time'
                      ? 'bg-black text-white'
                      : 'bg-white border border-gray-200'
                  }`}
                >
                  One-Time
                </button>
                <button
                  onClick={() => setDonationType('Subscription')}
                  className={`px-4 py-3 rounded-lg ${
                    donationType === 'Subscription'
                      ? 'bg-black text-white'
                      : 'bg-white border border-gray-200'
                  }`}
                >
                  Monthly
                </button>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <label className="block text-gray-700 text-sm font-semibold">
                Donation amount:
              </label>
              <div className="flex items-center flex-wrap gap-2 text-sm font-semibold">
                {presetAmounts.map((preset) => (
                  <button
                    key={preset}
                    onClick={() => {
                      setAmount(preset)
                      setShowCustomAmount(false)
                    }}
                    className={`px-4 py-2 text-xs md:text-sm w-max rounded-lg ${
                      amount === preset && !showCustomAmount
                        ? 'bg-black text-white'
                        : 'bg-white border border-gray-200'
                    }`}
                  >
                    ${preset}
                  </button>
                ))}
                <button
                  onClick={() => setShowCustomAmount(true)}
                  className={`px-4 w-max py-2 rounded-lg ${
                    showCustomAmount
                      ? 'bg-black text-white'
                      : 'bg-white border border-gray-200'
                  }`}
                >
                  Custom amount
                </button>
              </div>
              {showCustomAmount && (
                <input
                  type="number"
                  value={amount == 0 ? '' : amount}
                  onChange={(e) => setAmount(Number(e.target.value))}
                  className="w-full p-2 border rounded-lg text-sm font-semibold"
                  placeholder="Enter custom amount"
                  required
                />
              )}
            </div>
          </div>
          <CustomTooltip
            text="Verify your email by clicking the verify button."
            isVisible={!user && !isEmailVerified}
          >
            <CustomButton
              title="Donate"
              height={12}
              onClick={handleDonation}
              isLoading={
                donationResponse.isFetching ||
                nonLoggedinDonationResponse.isFetching
              }
              isDisabled={isDisabled}
            />
          </CustomTooltip>
        </div>
        <DonationInfo />
      </div>
    </div>
  )
}

export default DonationPage
