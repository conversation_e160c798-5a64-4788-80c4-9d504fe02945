'use client'
import React, { useMemo } from 'react'
import TeacherBanner from '../TeacherBanner/TeacherBanner'
import Share from '../Icons/Share'
import { toast } from 'react-toastify'
import ClassProgress from '../ClassProgress/ClassProgress'
import Person from '../Icons/Person'
import Class from '../Icons/Class'
import CalendarIcon from '../Icons/CalendarIcon'
import {
  formatDateTimeRange,
  formatDateToAmerican,
  formatSalesforceTime,
} from '@/utils/utils'
import Clock from '../Icons/Clock'
import { TEACHER_CLASS_TABS } from '@/utils/constants'
import RightColumnHoc from '../RightColumnHoc/RightColumnHoc'
import TeacherClassDetailRightSide from './TeacherClassDetailRightSide'
import ClassTabs from '../ClassTabs/ClassTabs'
import useModal from '@/hooks/useModal'
import AttendanceModalContent from '../AttendanceModalContent/AttendanceModalContent'
import MarkAttendanceModal from '../MarkAttendanceModal/MarkAttendanceModal'
import Link from 'next/link'
import AddressModal from '../AddressModal/AddressModal'
import EyeIcon from '../Icons/EyeIcon'
import { useAuth } from '@/contexts/AuthContext'
import { CLASS_EVENT_TYPE } from '@/types/enum'

interface TeacherClassDetailProps {
  classDetails: any
}

const TeacherClassDetail = ({ classDetails }: TeacherClassDetailProps) => {
  const [modal, showModal] = useModal()
  const [addressModal, showAddressModal] = useModal()
  const [pastMeetings, setPastMeetings] = React.useState<any>(
    classDetails?.pastMeetings || [],
  )
  const { user } = useAuth()

  const handleShare = () => {
    const classLink = `${process.env.NEXT_PUBLIC_APP_URL}/classes/${classDetails?.Id}`
    const isOnline = classDetails?.Type__c === 'Online'
    const isHybrid = classDetails?.Type__c === 'Hybrid'
    let textToCopy = ''
    let toastMsg = ''

    if (isHybrid || isOnline) {
      textToCopy = `Class Link:\n${classLink}\n\nZoom Link:\n${classDetails?.Zoom_Meeting_Link__c || ''}`
      toastMsg = 'Class & Zoom Link copied to clipboard'
    } else {
      textToCopy = classLink
      toastMsg = 'Class Link copied to clipboard'
    }

    navigator.clipboard.writeText(textToCopy)
    toast.success(toastMsg, {
      autoClose: 2500,
    })
  }

  const handleModal = (
    meetingId: string,
    markedAttendance: boolean,
    countType: boolean = false,
  ) => {
    showModal({
      title: ``,
      contentFn: (onClose) =>
        countType ? (
          <AttendanceModalContent
            meetingId={meetingId}
            onClose={onClose}
            isNotesRequired={
              classDetails?.Category__c === CLASS_EVENT_TYPE.YOUTH
            }
          />
        ) : (
          <MarkAttendanceModal
            onClose={onClose}
            meetingId={meetingId}
            markedAttendance={markedAttendance}
            classAttendees={classDetails?.classAttendees}
            setPastMeetings={setPastMeetings}
            isNotesRequired={
              classDetails?.Category__c === CLASS_EVENT_TYPE.YOUTH
            }
          />
        ),
      closeOnClickOutside: true,
      size: 'lg',
      rounded: '2xl',
      padding: 0,
      showCloseButton: false,
    })
  }

  const classTime = formatSalesforceTime(
    classDetails?.upcomingMeetings[0]?.Starts_On__c,
    classDetails?.upcomingMeetings[0]?.Ends_On__c,
  )

  const nextMeetingDate = useMemo(() => {
    const nextMeetingStart =
      classDetails?.upcomingMeetings &&
      classDetails?.upcomingMeetings[0]?.Starts_On__c
    const nextMeetingEnd =
      classDetails?.upcomingMeetings &&
      classDetails?.upcomingMeetings[0]?.Ends_On__c
    return nextMeetingStart
      ? formatDateTimeRange(nextMeetingStart, nextMeetingEnd)
      : ''
  }, [classDetails])

  const notMarkedAttendanceMeeting = useMemo(() => {
    let notMarkedMeeting: any = null
    const pastMeetings = classDetails?.pastMeetings || []
    for (const meeting of pastMeetings) {
      if (
        !meeting?.Attendance_Marked__c &&
        user &&
        meeting?.Teacher__r?.Id === user?.id
      ) {
        notMarkedMeeting = meeting
        break
      }
    }
    return notMarkedMeeting
  }, [classDetails, user])

  return (
    <div className="grid grid-cols-1 lg:grid-cols-10 gap-5">
      {/** Left side */}
      <div className="col-span-1 lg:col-span-7 bg-white rounded-2xl p-5 md:p-7 space-y-5">
        {classDetails?.Category__c && (
          <div className="rounded-md bg-color-yellow-3 px-2 py-1 w-max flex items-center">
            <span className="px-2 py-1 font-bold text-[10px] leading-[15px] text-center align-middle text-color-grey-14">
              {classDetails?.Category__c}
            </span>
          </div>
        )}
        <h1 className="font-bold text-2xl md:text-3xl">
          {classDetails?.Title__c}
        </h1>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between mt-4 gap-3">
          <div className="flex flex-col md:flex-row md:items-center gap-4">
            {(classDetails?.associatedTeachers || []).length > 0 && (
              <TeacherBanner teacherData={classDetails?.associatedTeachers} />
            )}
            <div
              className="hidden md:block min-h-[48px] h-auto border-l border-color-grey-2 mx-2 self-stretch"
              style={{ width: '2.5px' }}
            />
            {classDetails?.Genre__c && (
              <div
                className="flex flex-col md:flex-row"
                title={classDetails?.Genre__c}
              >
                <span className="text-sm text-color-grey-1">
                  {classDetails?.Genre__c ?? ''}
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
          <div className="flex flex-col">
            <span className="text-sm font-medium">Next Meeting</span>
            <h2 className="font-semibold text-xl md:text-2xl">
              {nextMeetingDate || 'No upcoming meeting'}
            </h2>
          </div>
          <div className="flex flex-wrap items-center gap-3 md:gap-5">
            {(classDetails?.Type__c === 'Online' ||
              classDetails?.Type__c === 'Hybrid') &&
              classDetails?.Zoom_Meeting_Link__c && (
                <Link
                  href={classDetails?.Zoom_Meeting_Link__c || '#'}
                  target="_blank"
                  className="h-10 md:h-12 flex items-center justify-center text-xs md:text-sm font-semibold bg-black text-white rounded-xl px-4 md:px-6"
                >
                  Start Zoom Meeting
                </Link>
              )}
            <Share onClick={handleShare} showBorder={true} />
          </div>
        </div>

        <ClassProgress
          completedCount={classDetails?.pastMeetings?.length}
          totalCount={classDetails?.Total_Meetings_Count__c}
        />

        {/** Hybrid / In-person / Online Section */}
        {classDetails?.Type__c === 'Hybrid' ? (
          <div className="flex flex-col md:flex-row items-stretch justify-between gap-5 w-full">
            <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 flex items-center justify-between w-full">
              <div className="space-y-3">
                <h2 className="text-sm text-color-grey-12">In Person</h2>
                <h3 className="text-sm font-bold">
                  {classDetails?.Room__c ? (
                    <span>{classDetails.Room__c}</span>
                  ) : null}
                  {classDetails?.venue?.name ? (
                    <span>
                      {classDetails?.Room__c ? ', ' : ''}
                      {classDetails.venue.name}
                    </span>
                  ) : null}
                </h3>
              </div>
              <button
                className="ml-2 p-2 rounded hover:bg-gray-200"
                onClick={() =>
                  showAddressModal({
                    title: '',
                    contentFn: (onClose) => (
                      <AddressModal
                        room={classDetails?.Room__c}
                        venueName={classDetails?.venue?.name}
                        address={classDetails?.venue?.address}
                        onClose={onClose}
                      />
                    ),
                    size: 'md',
                    rounded: '2xl',
                    padding: 0,
                    showCloseButton: false,
                  })
                }
                title="Show full address"
              >
                <EyeIcon />
              </button>
            </div>
            <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 w-full">
              <h2 className="text-sm text-color-grey-12">Online</h2>
              <h3 className="text-sm font-bold">
                {classDetails?.Zoom_Room__c ?? 'N/A'}
              </h3>
            </div>
          </div>
        ) : (
          <div className="bg-color-grey-10 rounded-2xl p-5 space-y-3 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-sm text-color-grey-12">
                {classDetails?.Type__c === 'Online' ? 'Online' : 'In Person'}
              </h2>
              <h3 className="text-sm font-bold">
                {classDetails?.Type__c === 'Online' ? (
                  (classDetails?.Zoom_Room__c ?? 'N/A')
                ) : (
                  <>
                    {classDetails?.Room__c && (
                      <span>{classDetails.Room__c}</span>
                    )}
                    {classDetails?.venue?.name && (
                      <span>
                        {classDetails?.Room__c ? ', ' : ''}
                        {classDetails.venue.name}
                      </span>
                    )}
                  </>
                )}
              </h3>
            </div>
            {classDetails?.Type__c !== 'Online' && (
              <button
                className="mt-2 md:mt-0 ml-0 md:ml-2 p-2 rounded hover:bg-gray-200 self-start md:self-auto"
                onClick={() =>
                  showAddressModal({
                    title: '',
                    contentFn: (onClose) => (
                      <AddressModal
                        room={classDetails?.Room__c}
                        venueName={classDetails?.venue?.name}
                        address={classDetails?.venue?.address}
                        onClose={onClose}
                      />
                    ),
                    size: 'md',
                    rounded: '2xl',
                    padding: 0,
                    showCloseButton: false,
                  })
                }
                title="Show full address"
              >
                <EyeIcon />
              </button>
            )}
          </div>
        )}
        {addressModal}

        {/** Details row */}
        <div className="flex flex-wrap gap-3 md:gap-5 mt-4 text-xs md:text-sm items-center justify-between">
          <div className="flex items-center gap-1">
            <Person />
            <span>
              {classDetails?.Total_Seats__c - classDetails?.Booked_Seats__c} out
              of {classDetails?.Total_Seats__c ?? ''} seats
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Class />
            <span>{classDetails?.Total_Meetings_Count__c ?? ''} Meetings</span>
          </div>
          <div className="flex items-center gap-1">
            <CalendarIcon />
            <span>
              {formatDateToAmerican(classDetails?.Start_Date__c)} -{' '}
              {formatDateToAmerican(classDetails?.End_Date__c)}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Clock />
            <span>{classTime}</span>
          </div>
        </div>

        <ClassTabs
          classDetails={classDetails}
          tabs={TEACHER_CLASS_TABS}
          pastMeetings={pastMeetings}
          handleModal={handleModal}
        />
      </div>

      {/** Right side */}
      <div className="col-span-1 lg:col-span-3">
        <RightColumnHoc>
          <TeacherClassDetailRightSide
            classType={classDetails?.Category__c}
            totalEnrolled={classDetails?.Total_Seats__c}
            totalAttended={classDetails?.Booked_Seats__c}
            notMarkedAttendanceMeeting={notMarkedAttendanceMeeting}
            handleModal={handleModal}
            attendees={classDetails?.classAttendees || []}
            mailingLists={classDetails?.classMailingLists || []}
          />
        </RightColumnHoc>
      </div>
      {modal}
    </div>
  )
}

export default TeacherClassDetail
