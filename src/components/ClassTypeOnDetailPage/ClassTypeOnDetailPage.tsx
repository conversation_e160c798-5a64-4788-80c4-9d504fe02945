import React from 'react'
import Person from '../Icons/Person'
import VideoPlayerIcon from '../Icons/VideoPlayerIcon'
import FilledVideoPlayerIcon from '../Icons/FilledVideoPlayerIcon'

const ClassTypeOnDetailPage = ({ type }: { type: string }) => {
  const classes = 'flex items-center justify-start w-full h-full gap-2'
  switch (type) {
    case 'In-Person':
      return (
        <div className={classes}>
          <Person />
          <h2 className="font-medium text-sm text-color-black">In-Person</h2>
        </div>
      )
    case 'Online':
      return (
        <div className={classes}>
          <VideoPlayerIcon />
          <h2 className="font-medium text-sm text-color-black">Online</h2>
        </div>
      )
    case 'Hybrid':
      return (
        <div className={classes}>
          <FilledVideoPlayerIcon />
          <h2 className="font-medium text-sm text-color-black">Hybrid</h2>
        </div>
      )
    default:
      return (
        <div className={classes}>
          <Person />
          <h2 className="font-medium text-sm text-color-black">Unknown</h2>
        </div>
      )
  }
}

export default ClassTypeOnDetailPage
