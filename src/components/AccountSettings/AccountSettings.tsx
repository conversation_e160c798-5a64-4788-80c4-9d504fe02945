// app/account/page.tsx
'use client'
import styles from './AccountSettings.module.css'

interface AccountSettings {
  currentPassword: string
  newPassword: string
  username: string
  email: string
  language: string
}

export default function AccountSettings() {
  return (
    <div className="">
      <h1 className="text-lg font-bold mb-6">Account Settings</h1>
      {/* Delete Account Section */}
      <div className="bg-white rounded-lg mb-6 border-b-1 border-color-grey pb-7">
        <div className="flex justify-between items-center mb-1">
          <h2 className="text-base font-medium">Delete Your Account</h2>
        </div>
        <p className="text-color-grey-1 text-sm mb-4 w-9/12">
        If you choose to delete your account, you will no longer have access to The Muse Writers Center’s online services, and all of your personal data will be permanently removed from our system. You have a 14-day grace period to change your mind and cancel the deletion before it becomes final.
        </p>
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <div className={styles.container}>
              <div className={styles.checkbox}>
                <label
                  htmlFor={`customCheckbox1`}
                  className={styles.checkbox__label}
                  key={`checkbox-1`}
                >
                  <div className={styles.checkbox__group}>
                    <input
                      type="checkbox"
                      className={styles.checkbox__input}
                      id={`customCheckbox1`}
                      hidden
                    />
                    <span className={styles.checkbox__checked}></span>
                  </div>
                </label>
              </div>
            </div>
            <label className="text-xs">
              Confirm that I want to delete my account.
            </label>
          </div>

          <div className="flex gap-4">
            <button className="h-12 w-40 rounded-xl border text-sm font-medium border">
              Learn More
            </button>
            <button
              onClick={() => {}}
              disabled={false}
              className={`h-12 w-40 rounded-xl border text-sm font-medium border-color-grey bg-color-yellow`}
            >
              Delete Account
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
