import Link from 'next/link'
import ForwardArrow from '../Icons/ForwardArrow'

const AuthorCTA = () => {
  return (
    <Link
      href={{
        pathname: '/settings',
        query: { tab: 'Membership' },
      }}
    >
      <div className="bg-black text-white rounded-[50px] py-[29px] px-[46px] flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div className="w-full md:w-[423px] space-y-4">
          <h2 className="text-3xl font-bold leading-[48px] text-neutral-white">
            Want your own author page? Become a Muse member.
          </h2>
          <p className="text-sm md:text-base leading-relaxed text-gray-300">
            Unlock your potential by becoming a member of The Muse. Create your
            own author page and connect with a vibrant community of teachers,
            sharing your knowledge and inspiring creativity in others.
          </p>
        </div>

        <button className="w-[212px] h-[50px] px-6 py-2.5 rounded-full text-sm font-semibold bg-color-yellow-1 text-color-black-3 flex items-center gap-3">
          Become a Member <ForwardArrow />
        </button>
      </div>
    </Link>
  )
}

export default AuthorCTA
