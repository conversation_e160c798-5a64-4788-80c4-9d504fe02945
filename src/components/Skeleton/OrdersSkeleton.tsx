import React from 'react'

type OrdersSkeletonProps = {
  count?: number
}

export default function OrdersSkeleton({ count = 3 }: OrdersSkeletonProps) {
  return (
    <>
      {Array.from({ length: count }).map((_, idx) => (
        <div
          key={idx}
          className="bg-white rounded-2xl w-full h-max mb-5 animate-pulse"
        >
          {/* Header Section */}
          <div className="flex justify-between w-full p-5">
            <div className="flex items-center justify-center gap-10">
              <div className="flex flex-col">
                <div className="h-4 bg-gray-200 rounded w-20 mb-1" />
                <div className="h-4 bg-gray-200 rounded w-24" />
              </div>
              <div className="flex flex-col">
                <div className="h-4 bg-gray-200 rounded w-16 mb-1" />
                <div className="h-4 bg-gray-200 rounded w-20" />
              </div>
              <div className="flex flex-col">
                <div className="h-4 bg-gray-200 rounded w-12 mb-1" />
                <div className="h-4 bg-gray-200 rounded w-16" />
              </div>
            </div>
            <div className="h-4 bg-gray-200 rounded w-20" />
          </div>

          {/* Divider */}
          <div className="bg-color-grey-2 border w-full"></div>

          {/* Content Section */}
          <div className="p-5">
            {/* Order Items Skeleton */}
            <div className="space-y-4 mb-4">
              {[...Array(1)].map((__, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-5 bg-gray-200 rounded w-3/4" />
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-4 bg-gray-200 rounded w-1/3" />
                </div>
              ))}
            </div>

            {/* Dotted Divider */}
            <div className="border-1 border-dotted w-full mb-4"></div>

            {/* Bottom Section - Two Column Layout */}
            <div className="flex items-center justify-between mt-4">
              {/* Left Side - Billing Address & Payment Method */}
              <div>
                {/* Billing Address */}
                <div className="mb-4">
                  <div className="h-4 bg-gray-200 rounded w-28 mb-2" />
                  <div className="space-y-1">
                    <div className="h-3 bg-gray-200 rounded w-32" />
                    <div className="h-3 bg-gray-200 rounded w-28" />
                    <div className="h-3 bg-gray-200 rounded w-24" />
                  </div>
                </div>

                {/* Payment Method */}
                <div>
                  <div className="h-4 bg-gray-200 rounded w-32 mb-2" />
                  <div className="flex items-center gap-2">
                    <div className="w-12 h-8 bg-gray-200 rounded" />
                    <div className="h-3 bg-gray-200 rounded w-40" />
                  </div>
                </div>
              </div>

              {/* Right Side - Totals Section */}
              <div className="border-t pt-4 flex flex-col gap-2">
                <div className="flex justify-between text-gray-700 gap-7">
                  <div className="h-4 bg-gray-200 rounded w-20" />
                  <div className="h-4 bg-gray-200 rounded w-16" />
                </div>
                <div className="flex justify-between gap-7">
                  <div className="h-3 bg-gray-200 rounded w-28" />
                  <div className="h-3 bg-gray-200 rounded w-12" />
                </div>
                <div className="flex justify-between mt-2 gap-7">
                  <div className="h-4 bg-gray-200 rounded w-24" />
                  <div className="h-4 bg-gray-200 rounded w-16" />
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </>
  )
}
