'use client'
import React, { useEffect, useState } from 'react'
import NoResult from '../NoResult/NoResult'
import WalletIcon from '../Icons/WalletIcon'
import AddPaymentMethod from '../AddPaymentMethod/AddPaymentMethod'
import PaymentMethodCard from '../PaymentMethodCard/PaymentMethodCard'
import PaymentMethodCardSkeleton from '../Skeletons/PaymentMethodCardSkeleton'
import { useApi } from '@/hooks/useApi'
import {
  addPaymentMethod,
  getAllPaymentMethods,
} from '@/lib/actions/customerProfile.actions'
import { toast } from 'react-toastify'

const PaymentSettings = () => {
  const [showAddPaymentMethod, setShowAddPaymentMethod] =
    useState<boolean>(false)
  const [paymentMethods, setPaymentMethods] = useState<any>([])
  const [paymentResponse, getPaymentData] = useApi((access: string) =>
    getAllPaymentMethods(access),
  )
  const [addCardResponse, addCard] = useApi(addPaymentMethod)

  useEffect(() => {
    if (paymentResponse.isSuccess) {
      setPaymentMethods(
        paymentResponse?.data?.data?.paymentProfiles?.paymentProfiles || [],
      )
    }
    if (addCardResponse.isSuccess) {
      toast.success('Card added successfully', { autoClose: 2000 })
      setShowAddPaymentMethod(!showAddPaymentMethod)
    }
  }, [paymentResponse, addCardResponse])

  useEffect(() => {
    getPaymentData()
  }, [])

  const buttonClick = () => {
    setShowAddPaymentMethod(!showAddPaymentMethod)
  }

  const onSubmit = (data: any) => {
    const { cardNumber, expiration, securityCode } = data
    addCard(
      (cardNumber as string).replaceAll('-', ''),
      expiration,
      securityCode,
    )
  }
  return (
    <div className="w-full flex items-center justify-center flex-col">
      {paymentResponse.isFetching ? (
        <div className="mt-5 flex w-3/4 flex-col items-center justify-center gap-5">
          <PaymentMethodCardSkeleton />
          <PaymentMethodCardSkeleton />
          <PaymentMethodCardSkeleton />
        </div>
      ) : (
        <>
          {paymentMethods.length > 0 && !showAddPaymentMethod && (
            <>
              <div className="flex  w-3/4 flex-col gap-5 items-center justify-center  mt-5">
                {paymentMethods.map((item: any, index: number) => (
                  <PaymentMethodCard
                    data={item}
                    key={index}
                    setPaymentMethods={setPaymentMethods}
                  />
                ))}
              </div>
              <button
                className="w-3/4 inline-flex items-center bg-color-yellow justify-center whitespace-nowrap disabled:pointer-events-none disabled:opacity-50 h-12  rounded-md text-sm focus-visible:outline-none focus-visible:ring-1 text-black focus-visible:ring-ring font-semibold shadow px-4 py-2 mt-6"
                onClick={() => setShowAddPaymentMethod(!showAddPaymentMethod)}
              >
                Add Payment Method
              </button>
            </>
          )}
          {paymentMethods.length === 0 && !showAddPaymentMethod && (
            <NoResult
              icon={<WalletIcon />}
              title="No payment methods added"
              desc="You haven’t added any payment details yet. Add a card make future transactions faster and easier"
              btnText="Add Payment Method"
              btnClick={buttonClick}
            />
          )}
          {showAddPaymentMethod && (
            <AddPaymentMethod
              onSubmit={onSubmit}
              addCardResponse={addCardResponse}
            />
          )}
        </>
      )}
    </div>
  )
}

export default PaymentSettings
