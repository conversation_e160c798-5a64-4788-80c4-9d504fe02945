'use client'
import { useState, FormEvent } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import CustomDatePicker from '../CustomComponents/CustomDatePicker/CustomDatePicker'
import { createTeenApplication } from '@/lib/actions/account.actions'
import { useApi } from '@/hooks/useApi'
import GreenTick from '../Icons/GreenTick'
import MobileLogin from '../MobileLogin/MobileLogin'
import EmailVerify from '../EmailVerify/EmailVerify'
import { getPresignedPutUrl, getPublicUrl } from '@/lib/aws'
import { toast } from 'react-toastify'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'

export default function FellowshipApplication({ userData }: { userData: any }) {
  const userNameArray = userData ? userData.name.split(' ') : ['', '']

  const [formData, setFormData] = useState({
    firstName: userData ? userNameArray[0] : '',
    lastName: userData ? userNameArray[userNameArray.length - 1] : '',
    pronouns: '',
    parentFirstName: '',
    parentLastName: '',
    parentPhoneNumber: '',
    parentEmail: '',
    zipCode: userData ? userData?.BillingAddress?.postalCode : '',
    highSchool: '',
    gradeLevel: '',
    enjoyWriting: '',
    creativeWritingFuture: '',
    whyParticipate: '',
    needTransport: false,
    signature: '',
  })
  const [dateOfBirth, setDateOfBirth] = useState<string>(
    userData ? userData?.birthday : '',
  )
  const [isPhoneVerified, setIsPhoneVerified] = useState<boolean>(
    !!userData?.phoneNumber,
  )
  const [isEmailVerified, setIsEmailVerified] = useState<boolean>(
    !!userData?.email,
  )
  const [phoneNumber, setPhoneNumber] = useState<string>(
    userData ? userData?.phoneNumber : '',
  )
  const [email, setEmail] = useState<string>(userData ? userData?.email : '')
  const [fileLink, setFileLink] = useState<string>('')
  const [isFileUploading, setIsFileUploading] = useState<boolean>(false)
  const [fileName, setFileName] = useState<string>('')

  const [response, createTeenApplicationForm] = useApi(
    (
      firstName: string,
      lastName: string,
      phoneNumber: string,
      email: string,
      pronouns: string,
      parentFirstName: string,
      parentLastName: string,
      parentPhoneNumber: string,
      parentEmail: string,
      zipCode: string,
      highSchool: string,
      dateOfBirth: string,
      gradeLevel: string,
      essayResponses: string[],
      needTransport: boolean,
    ) =>
      createTeenApplication(
        firstName,
        lastName,
        phoneNumber,
        email,
        pronouns,
        parentFirstName,
        parentLastName,
        parentPhoneNumber,
        parentEmail,
        zipCode,
        highSchool,
        dateOfBirth,
        gradeLevel,
        essayResponses,
        needTransport,
        fileLink,
      ),
    false,
  )

  const handleInputChange = (
    e:
      | React.ChangeEvent<
          HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
        >
      | { target: { name: string; value: string | boolean } },
  ) => {
    const { name, value } = e.target
    if (name === 'zipCode' && typeof value === 'string' && value.length > 6)
      return
    if (name === 'zipCode' && typeof value === 'string' && !/^\d*$/.test(value))
      return
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }))
  }

  const handlePhoneChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      parentPhoneNumber: value,
    }))
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    setIsFileUploading(true)
    if (file) {
      const timeStamp = new Date().getTime()
      const fileName = `resume_${timeStamp}_${file.name}`
      try {
        const presignedUrl = await getPresignedPutUrl(fileName, file.type)
        const uploadResponse = await fetch(presignedUrl, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': file.type,
          },
        })

        if (uploadResponse.ok) {
          const resumeLink = await getPublicUrl(fileName)
          setFileLink(resumeLink)
          setFileName(fileName)
          toast?.success('Resume uploaded successfully!', { autoClose: 1500 })
        }
      } catch (error) {
        toast?.error('Error uploading resume: ' + error, { autoClose: 1500 })
      } finally {
        setIsFileUploading(false)
      }
    }
  }

  // New onClick handler for CustomButton
  const handleButtonClick = async () => {
    // Optional: Add custom validation or actions
    if (!isPhoneVerified || !isEmailVerified) {
      toast.error('Please verify both phone and email before submitting.')
      return
    }

    if (
      !formData.firstName ||
      !formData.lastName ||
      !formData.pronouns ||
      !formData.parentFirstName ||
      !formData.parentLastName ||
      !formData.parentPhoneNumber ||
      !formData.parentEmail ||
      !formData.zipCode ||
      !formData.highSchool ||
      !dateOfBirth ||
      !formData.gradeLevel ||
      !formData.enjoyWriting ||
      !formData.creativeWritingFuture ||
      !formData.whyParticipate ||
      !formData.signature ||
      !fileLink
    ) {
      toast.error('Please fill out all required fields and upload a file.')
      return
    }
    try {
      await createTeenApplicationForm(
        formData.firstName,
        formData.lastName,
        phoneNumber,
        email,
        formData.pronouns,
        formData.parentFirstName,
        formData.parentLastName,
        formData.parentPhoneNumber,
        formData.parentEmail,
        formData.zipCode,
        formData.highSchool,
        dateOfBirth,
        formData.gradeLevel,
        [
          formData.enjoyWriting,
          formData.creativeWritingFuture,
          formData.whyParticipate,
        ],
        formData.needTransport,
      )
      setFormData({
        firstName: '',
        lastName: '',
        pronouns: '',
        parentFirstName: '',
        parentLastName: '',
        parentPhoneNumber: '',
        parentEmail: '',
        zipCode: '',
        highSchool: '',
        gradeLevel: '',
        enjoyWriting: '',
        creativeWritingFuture: '',
        whyParticipate: '',
        needTransport: false,
        signature: '',
      })
      setDateOfBirth('')
      toast?.success('Application submitted successfully!', { autoClose: 1500 })
      setFileLink('')
      setIsPhoneVerified(false)
      setIsEmailVerified(false)
      setPhoneNumber('')
      setEmail('')
      setIsFileUploading(false)
    } catch (err) {
      console.error(err)
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Fellowship Application 2025</h1>

      <div className="mb-6">
        <p className="mb-2">
          The 2025 Fellowship Submission Window is now closed.
        </p>

        <p className="mb-2">
          Please answer the following questions in detail. Your writing sample
          of poetry, creative nonfiction, or fiction, should be no more than
          five total pages (maximum 1,500 words). Your submission package must
          include: creative work, a completed application form, signed
          parent/guardian consent (if under 18), a brief bio, and your sample of
          work. You can send your writing sample as an attachment to the Youth
          Program Director Jessica Grace <NAME_EMAIL>.
        </p>

        <p className="mb-2">
          Any submissions that do not follow guidelines will not be considered.
        </p>

        <p className="mb-4">
          The deadline has passed, and applications are closed. Applicants will
          be contacted with results by February 8, 2025.
        </p>

        <p className="mb-4">
          Questions can be directed to our Youth Program Director, Jessica Grace
          Kelley <EMAIL>.
        </p>
      </div>

      <form className="space-y-6">
        {/* Applicant Name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              htmlFor="firstName"
              className="block mb-1 text-sm font-medium"
            >
              Applicant Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              placeholder="First"
              value={formData.firstName}
              onChange={handleInputChange}
              required
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
          <div className="mt-0">
            <label
              htmlFor="lastName"
              className="block mb-1 text-sm font-medium md:invisible"
            >
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              placeholder="Last"
              value={formData.lastName}
              onChange={handleInputChange}
              required
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
        </div>

        {/* Applicant Pronouns */}
        <div>
          <label htmlFor="pronouns" className="block mb-1 text-sm font-medium">
            Applicant Pronouns <span className="text-red-500">*</span>
          </label>
          <select
            id="pronouns"
            name="pronouns"
            value={formData.pronouns}
            onChange={handleInputChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
          >
            <option value="">Select pronouns</option>
            <option value="he/him">he/him</option>
            <option value="she/her">she/her</option>
            <option value="they/them">they/them</option>
            <option value="prefer not to say">Prefer not to say</option>
          </select>
        </div>

        {/* Parent/Guardian Name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              htmlFor="parentFirstName"
              className="block mb-1 text-sm font-medium"
            >
              Parent/Guardian Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="parentFirstName"
              name="parentFirstName"
              placeholder="First"
              value={formData.parentFirstName}
              onChange={handleInputChange}
              required
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
          <div className="mt-0">
            <label
              htmlFor="parentLastName"
              className="block mb-1 text-sm font-medium md:invisible"
            >
              Last Name
            </label>
            <input
              type="text"
              id="parentLastName"
              name="parentLastName"
              placeholder="Last"
              value={formData.parentLastName}
              onChange={handleInputChange}
              required
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
        </div>

        {/* Applicant Phone Number */}
        {isPhoneVerified ? (
          <div className="space-y-1">
            <label htmlFor="phoneNumber" className="block text-sm font-medium">
              Phone
            </label>
            <div className="relative">
              <input
                type="text"
                value={phoneNumber}
                readOnly={true}
                className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                placeholder="Enter your number"
                required
              />
              <span className="absolute right-3 top-3 text-sm font-semibold text-color-green-4 flex items-center gap-2 cursor-pointer">
                <GreenTick />
                Verified
              </span>
            </div>
          </div>
        ) : (
          <div className="teacher-onboarding">
            <MobileLogin
              showOtpBtn={false}
              setIsPhoneVerified={setIsPhoneVerified}
              setUserPhone={setPhoneNumber}
              title="Applicant Phone Number"
            />
          </div>
        )}

        {/* Parent/Guardian Phone Number */}
        <div>
          <label
            htmlFor="parentPhoneNumber"
            className="block mb-1 text-sm font-medium"
          >
            Parent/Guardian Phone Number <span className="text-red-500">*</span>
          </label>
          <PhoneInput
            country={'us'}
            value={formData.parentPhoneNumber}
            onChange={handlePhoneChange}
            inputProps={{
              name: 'parentPhoneNumber',
              required: true,
              className: 'w-full p-2 border border-gray-300 rounded pl-12',
            }}
          />
        </div>

        {/* Applicant Email */}
        <EmailVerify
          user={null}
          email={email}
          setEmail={setEmail}
          phoneNumber={phoneNumber}
          studentOnBoarding={false}
          setIsEmailVerified={setIsEmailVerified}
          isEmailVerified={isEmailVerified}
          title="Applicant Email"
        />

        {/* Parent/Guardian Email */}
        <div>
          <label
            htmlFor="parentEmail"
            className="block mb-1 text-sm font-medium"
          >
            Parent/Guardian Email <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            id="parentEmail"
            name="parentEmail"
            value={formData.parentEmail}
            onChange={handleInputChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>

        {/* Zip Code */}
        <div>
          <label htmlFor="zipCode" className="block mb-1 text-sm font-medium">
            Zip Code <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="zipCode"
            name="zipCode"
            value={formData.zipCode}
            onChange={handleInputChange}
            required
            maxLength={6}
            pattern="\d*"
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>

        {/* High School */}
        <div>
          <label
            htmlFor="highSchool"
            className="block mb-1 text-sm font-medium"
          >
            Name of High School <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="highSchool"
            name="highSchool"
            value={formData.highSchool}
            onChange={handleInputChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>

        {/* Birth date */}
        <CustomDatePicker
          title="Date of Birth"
          setDateOfBirth={setDateOfBirth}
          dateOfBirth={dateOfBirth}
        />

        {/* Grade Level */}
        <div>
          <label
            htmlFor="gradeLevel"
            className="block mb-1 text-sm font-medium"
          >
            Are you a Sophomore or Junior?{' '}
            <span className="text-red-500">*</span>
          </label>
          <select
            id="gradeLevel"
            name="gradeLevel"
            value={formData.gradeLevel}
            onChange={handleInputChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
          >
            <option value="">Select one</option>
            <option value="Sophomore">Sophomore</option>
            <option value="Junior">Junior</option>
          </select>
        </div>

        {/* Essay Questions */}
        <div>
          <label
            htmlFor="enjoyWriting"
            className="block mb-1 text-sm font-medium"
          >
            What do you enjoy about writing?{' '}
            <span className="text-red-500">*</span>
          </label>
          <textarea
            id="enjoyWriting"
            name="enjoyWriting"
            rows={4}
            value={formData.enjoyWriting}
            onChange={handleInputChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
          ></textarea>
        </div>

        <div>
          <label
            htmlFor="creativeWritingFuture"
            className="block mb-1 text-sm font-medium"
          >
            How do you see creative writing fitting into your future?{' '}
            <span className="text-red-500">*</span>
          </label>
          <textarea
            id="creativeWritingFuture"
            name="creativeWritingFuture"
            rows={4}
            value={formData.creativeWritingFuture}
            onChange={handleInputChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
          ></textarea>
        </div>

        <div>
          <label
            htmlFor="whyParticipate"
            className="block mb-1 text-sm font-medium"
          >
            Why do you want to participate in the Muse Fellowship?{' '}
            <span className="text-red-500">*</span>
          </label>
          <textarea
            id="whyParticipate"
            name="whyParticipate"
            rows={4}
            value={formData.whyParticipate}
            onChange={handleInputChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
          ></textarea>
        </div>

        {/* Transportation Services */}
        <div>
          <label className="block mb-2 text-sm font-medium">
            Will you need transportation services?{' '}
            <span className="text-red-500">*</span>
          </label>
          <p className="text-sm mb-2">
            <label
              htmlFor="needTransport"
              className="cursor-pointer flex items-center gap-2"
            >
              <input
                type="checkbox"
                name="needTransport"
                checked={formData.needTransport}
                onChange={handleCheckboxChange}
                className="h-4 w-4"
              />
              {`Transportation services are offered for free through The Muse's
            approved car service, Uber.`}
            </label>
          </p>

          <div className="border border-gray-300 rounded p-4">
            <p className="text-sm font-medium px-2">
              Check Dates to Confirm: Are you able to commit to below dates and
              times? (We allow 4 total absences during the Fellowship.)
            </p>

            <div className="space-y-2 mt-2">
              {[
                {
                  id: '2_15_25',
                  date: '2/15/25 Fellowship Orientation - Virtual (5-6pm)',
                },
                {
                  id: '2_27_25',
                  date: '2/27/25: 1st Fellowship Class - Thursday (6-8:30pm)',
                },
                {
                  id: '3_8_25',
                  date: '3/8/25: 1st Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '3_13_25',
                  date: '3/13/25 - Fellowship Class - Thursday (6-8:30pm)',
                },
                {
                  id: '3_15_25',
                  date: '3/15/25 - Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '3_22_25',
                  date: '3/22/25 - Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '3_27_25',
                  date: '3/27/25 - Fellowship Class - Thursday (6-8:30pm)',
                },
                {
                  id: '3_29_25',
                  date: '3/29/25: Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '4_5_25',
                  date: '4/5/25: Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '4_10_25',
                  date: '4/10/25: Fellowship Class - Thursday (6-8:30pm)',
                },
                {
                  id: '4_12_25',
                  date: '4/12/25 - Spring Teen Class READING - Saturday (10am-12:30pm)',
                },
                {
                  id: '4_24_25',
                  date: '4/24/25: Fellowship Class WORKSHOP - Thursday (6-8:30pm)',
                },
                {
                  id: '4_26_25',
                  date: '4/26/25 - 1st Summer Fellowship Class - Thursday (6-8:30pm)',
                },
                {
                  id: '7_10_25',
                  date: '7/10/25 - Fellowship Class - Thursday (6-8:30pm)',
                },
                {
                  id: '7_12_25',
                  date: '7/12/25 - Fellowship Class WORKSHOP - Saturday (10am-12:30pm)',
                },
                {
                  id: '7_19_25',
                  date: '7/19/25 - Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '7_24_25',
                  date: '7/24/25 - Fellowship Class - Thursday (6-8:30pm)',
                },
                {
                  id: '7_26_25',
                  date: '7/26/25 - Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '8_2_25',
                  date: '8/2/25 - Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '8_7_25',
                  date: '8/7/25 - Fellowship Class - Thursday (6-8:30pm)',
                },
                {
                  id: '8_9_25',
                  date: '8/9/25 - Teen Class - Saturday (10am-12:30pm)',
                },
                {
                  id: '8_14_25',
                  date: '8/14/25 - Summer Teen Class READING - Saturday (10am-12:30pm)',
                },
                {
                  id: '8_21_25',
                  date: '8/21/25: Fellowship Class READING - Thursday (6-8:30pm)',
                },
                {
                  id: '8_23_25',
                  date: 'TBA: attend free one-on-one meeting with professional literary agents or editors',
                },
                {
                  id: '8_28_25',
                  date: 'This will be a new schedule for FALL 2025. I know this information is part of the program.',
                },
              ].map((item) => (
                <label key={item.id} htmlFor={item.id}>
                  <div className="mb-2 flex gap-3">
                    <input
                      type="checkbox"
                      id={item.id}
                      name={item.id}
                      className="mt-1 mr-2 h-4 w-4"
                    />
                    <span className="text-base">{item.date}</span>
                  </div>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* File Upload */}
        <div>
          <label className="block mb-1 text-sm font-medium">
            Upload your creative writing submission here (please read guidelines
            below) <span className="text-red-500">*</span>
          </label>
          <div className="border border-gray-300 rounded p-4 flex flex-col items-center justify-center relative h-32">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-10 w-10 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
            <p className="mt-2 text-sm text-gray-500">
              {isFileUploading
                ? 'Uploading...'
                : fileLink
                  ? fileName
                  : 'Click or drag a file here to upload.'}
            </p>
            <input
              type="file"
              name="creativeWriting"
              accept=".pdf,.doc,.docx"
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              onChange={handleFileChange}
              required
            />
          </div>
          <p className="mt-2 text-xs text-gray-500">
            Files in .docx, .pdf format. The page submission should be no more
            document (five page maximum). Page numbers in the upper right
            corner. We only accept .pdf documents, doc or docx or page formats.
            Any submissions that do not follow guidelines will not be
            considered.
          </p>
        </div>

        <div className="mt-4">
          <p className="text-sm mb-4">
            Everything above is true to the best of my knowledge. Please sign
            below <span className="text-red-500">*</span>
          </p>
          <input
            type="text"
            id="signature"
            name="signature"
            value={formData.signature}
            onChange={handleInputChange}
            required
            placeholder="Type your full name as signature"
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>

        <div className="mt-6">
          <CustomButton
            title="Submit Application"
            isLoading={response.isFetching}
            onClick={handleButtonClick}
          />
        </div>
      </form>
    </div>
  )
}
