'use client'
import { usePathname } from 'next/navigation'
import { ROLES } from '@/types/enum'
import { NON_LOGGED_IN_PROTECTED_ROUTES } from '@/utils/constants'
import { useRef } from 'react'

interface LayoutWrapperProps {
  children: React.ReactNode
  role?: string
}

export default function LayoutWrapper({ children, role }: LayoutWrapperProps) {
  const pathname = usePathname()
  const roleRef = useRef<any>(role)
  const isTwoColumnPath = NON_LOGGED_IN_PROTECTED_ROUTES.includes(
    '/' + pathname.split('/')[1],
  )
  if (isTwoColumnPath) roleRef.current = null
  else roleRef.current = role

  // Determine the layout class based on both role and URL
  const layoutClass = [
    ROLES.Adult_Student,
    ROLES.Youth_Student,
    ROLES.Teacher,
    ROLES.Parent,
  ].includes(roleRef.current as ROLES)
    ? 'two-column'
    : 'one-column'

  return (
    <div className={`layout-container bg-color-grey ${layoutClass}`}>
      {children}
    </div>
  )
}
