'use client'
import { useUpdateParentTodoMutation } from '@/lib/redux/slices/apiSlice/apiSlice'
import { X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import useModal from '@/hooks/useModal'
import PaymentChoiceModal from './PaymentChoiceModal'
import { useRouter } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { setPaymentToken } from '@/lib/redux'
import CheckboxItem from './CheckboxItem'

const ClassRegistrationModal = ({
  onClose,
  item,
  setDeletedItems,
}: {
  onClose: () => void
  item: any
  setDeletedItems: React.Dispatch<React.SetStateAction<string[]>>
}) => {
  const router = useRouter()
  const dispatch = useDispatch()
  const [updateParentTodo, { isLoading, isSuccess, isError }] =
    useUpdateParentTodoMutation()
  const [modal, showModal] = useModal()
  const handleApprove = async (orderId: string, paidBy: 'Parent' | 'Child') => {
    const res = await updateParentTodo({
      isTokenRequired: true,
      orderId,
      type: 'Approved',
      paidBy,
    })
  }

  useEffect(() => {
    if (isSuccess) {
      toast.success('Class request approved successfully', { autoClose: 2000 })
      onClose()
    }
  }, [isSuccess])

  const [checkedItems, setCheckedItems] = useState<boolean[]>([])
  const [thirdHoveredOnce, setThirdHoveredOnce] = useState<boolean>(false)

  const toggleCheckbox = (index: number) => {
    setCheckedItems((prev) => {
      const newState = [...prev]
      newState[index] = !newState[index]
      return newState
    })
  }

  const handleProccedApproval = (item: any) => {
    if (Number(item?.TotalAmount) === 0) {
      handleApprove(item?.Id, 'Parent')
      setDeletedItems((prev) => [...prev, item?.Id])
    } else if (item?.Account__r?.Age__c < 13) {
      dispatch(setPaymentToken(item?.Checkout_JWT_Token__c))
      handleApprove(item?.Id, 'Parent')
      router.push(`/checkout?token=${item?.Checkout_JWT_Token__c}`)
    } else {
      showModal({
        title: ``,
        contentFn: (onClose) => (
          <PaymentChoiceModal
            onClose={onClose}
            onPaySelf={() => {
              dispatch(setPaymentToken(item?.Checkout_JWT_Token__c))
              handleApprove(item?.Id, 'Parent')
              router.push(`/checkout?token=${item?.Checkout_JWT_Token__c}`)
              onClose()
            }}
            onPayOther={() => {
              handleApprove(item?.Id, 'Child')
              onClose()
            }}
            studentName={item?.Account__r?.Name || 'Student'}
          />
        ),
        closeOnClickOutside: true,
        size: 'lg',
        rounded: '2xl',
        padding: 0,
        showCloseButton: false,
      })
    }
  }

  const requiredChecked =
    (checkedItems[0] && checkedItems[1] && thirdHoveredOnce) ||
    (checkedItems[0] && checkedItems[1] && checkedItems[2])

  const inputItems = [
    'Conduct: Profanity is not allowed during the Teen Fellowship, Teen classes, or Teen events. Freedom of expression is encouraged with appropriate language in your personal presentation and creative works. Use your best judgement when conducting yourself, keeping in mind mutual respect, punctuality, and an open mind. The Muse and its programs do not discriminate based on gender, race, religion, ethnicity, sexual orientation, or socio-economic status. We expect our students to demonstrate this basic respect as well.  Dress Code: Students and teachers are expected to wear appropriate clothing to class, camps, readings, and events. “Appropriate” clothing falls into the category of the dress codes of public schools. We encourage free expression, but with professionalism & tact for a focused learning environment.',
    'I agree as one of the conditions of my participation in the Teen Writers Fellowship or any other events associated with The Muse, to specifically release The Muse Writers Center, its students, teachers, and employees from any claims or liabilities of any kind whatsoever, arising from or related to my participation in classes & events. If I accept transportation assistance facilitated by The Muse Writers Center (such as an Uber, Lyft, or Taxi service), I agree the same terms apply during transit to and from The Muse Writers Center.',
    `I agree to grant to The Muse Writers Center and its authorized representatives permission to record photos of my participation and audio or video footage of my experience of The Muse classes, programs, and events. I further agree that any of the material photographed or recorded may be used in any form, as part of the nonprofit's future publications, social media content, or printed materials. I agree that such use shall be without payment of fees, royalties, special credit or other compensation.
    Note: If you prefer not to allow your child's picture to be used on the website, <NAME_EMAIL> to request an exception.`,
  ]

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50 p-3">
      <div className="bg-white rounded-2xl shadow-lg max-w-md w-full p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Class Registration Request</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
          >
            <X size={20} className="text-color-grey-1" />
          </button>
        </div>

        {/* Description */}
        <p className="font-normal text-sm text-color-black-4 mb-6">
          {item?.Account__r?.Name} has requested an approval to attend the
          following classes:
        </p>

        {/* Class List */}
        <div className="space-y-2 mb-6">
          {item?.orderItems?.records?.map((order: any, index: number) => {
            return (
              <div className="flex justify-between" key={index}>
                <span className="font-bold text-base tracking-normal align-middle">
                  {order?.Product2?.Name}
                </span>
                <span className="font-normal text-base text-color-grey-15 tracking-normal text-right align-middle">
                  ${order?.UnitPrice}
                </span>
              </div>
            )
          })}
        </div>

        <hr className="my-6" />

        {/* Totals */}
        <div className="flex justify-between text-color-grey-15">
          <span className="font-normal text-base">Cart Total</span>
          <span>${item?.TotalAmount}</span>
        </div>
        <div className="flex justify-between text-gray-500 mt-2">
          <span>Coupon Discount</span>
          <span>- ${item?.Discount__c || 0}</span>
        </div>
        <div className="flex justify-between font-bold text-base mt-2">
          <span>Total Due</span>
          <span>${item?.TotalAmount - item?.Discount__c}</span>
        </div>

        <div className="mt-5 space-y-3 text-sm">
          {inputItems?.map((text, index) => (
            <CheckboxItem
              key={index}
              text={text}
              checked={checkedItems[index] || false}
              onToggle={() => toggleCheckbox(index)}
              setThirdHoveredOnce={
                index === 2 ? setThirdHoveredOnce : undefined
              }
            />
          ))}
        </div>

        <div className="flex justify-end mt-6 gap-6">
          <CustomButton
            title={
              item?.Account__r?.Age__c < 13
                ? 'Proceed to Checkout'
                : 'Approve & Proceed'
            }
            onClick={() => handleProccedApproval(item)}
            isLoading={isLoading}
            height={10}
            width="222px"
            backgroundColor="bg-color-yellow"
            classes="text-color-black"
            isDisabled={!requiredChecked}
          />
        </div>
      </div>
      {modal}
    </div>
  )
}

export default ClassRegistrationModal
