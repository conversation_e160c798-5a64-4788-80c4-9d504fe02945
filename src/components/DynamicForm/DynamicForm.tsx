'use client'
import { Field } from '@/interfaces/formInterfaces'
import { createFormResponse } from '@/lib/actions/form.actions'
import { getPresignedPutUrl } from '@/lib/aws'
import { useRouter } from 'next/navigation'
import PropTypes from 'prop-types'
import { FormEvent, useState } from 'react'
import { toast } from 'react-toastify'

const DynamicForm = ({ id, data }: { id?: string; data: Field[] }) => {
  const router = useRouter()
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, string>>({})
  const [isFileUploading, setIsFileUploading] = useState<
    Record<string, boolean>
  >({})

  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldName: string,
  ) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check file size based on file type
    const isImage = file.type.startsWith('image/')
    const isPDF = file.type === 'application/pdf'

    let maxSize, fileTypeText
    if (isImage) {
      maxSize = 5 * 1024 * 1024 // 5MB for images
      fileTypeText = '5MB'
    } else if (isPDF) {
      maxSize = 10 * 1024 * 1024 // 10MB for PDFs
      fileTypeText = '10MB'
    } else {
      maxSize = 10 * 1024 * 1024 // Default 10MB for other files
      fileTypeText = '10MB'
    }

    if (file.size > maxSize) {
      toast.error(
        `File size must be less than ${fileTypeText}. Please choose a smaller file.`,
        {
          autoClose: 3000,
        },
      )
      // Clear the input
      e.target.value = ''
      return
    }

    setIsFileUploading({ ...isFileUploading, [fieldName]: true })
    const fileURL = `DynamicForm_${new Date().getTime()}_${file.name}`
    try {
      const presignedUrl = await getPresignedPutUrl(fileURL, file.type)
      const uploadResponse = await fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      })

      if (uploadResponse.ok) {
        const fileUrl = `https://writerai-staging.s3.amazonaws.com/${fileURL}`
        setUploadedFiles({ ...uploadedFiles, [fieldName]: fileUrl })
        toast.success('File uploaded successfully!', { autoClose: 1000 })
      } else {
        throw new Error('Upload failed')
      }
    } catch (error) {
      console.error('File upload error:', error)
      toast.error('File upload failed. Please try again.', { autoClose: 3000 })
    } finally {
      setIsFileUploading({ ...isFileUploading, [fieldName]: false })
    }
  }
  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.currentTarget)
    const formDataObject: Record<string, any> = {}

    formData.forEach((value, key) => {
      if (formDataObject[key]) {
        if (!Array.isArray(formDataObject[key])) {
          formDataObject[key] = [formDataObject[key].toString()]
        }
        formDataObject[key].push(value.toString())
      } else {
        formDataObject[key] = value.toString()
      }
    })

    // Add uploaded file URLs to form data
    Object.keys(uploadedFiles).forEach((fieldName) => {
      formDataObject[fieldName] = uploadedFiles[fieldName]
    })

    const formattedData = data.map((field) => ({
      type: field.type,
      required: field.required,
      label: field.label,
      className: field.className,
      name: field.name,
      value: formDataObject[field.name] || '',
    }))
    if (id) {
      try {
        const response = await createFormResponse(id, formattedData)
        toast.success('Form successfully submitted!', { autoClose: 1000 })
      } catch (error) {
        toast.error('Something went wrong!' + error, { autoClose: 1500 })
        console.error('Error saving form response:', error)
      }
    }
  }

  const renderField = (field: Field) => {
    switch (field.type) {
      case 'select':
        const optionArray =
          typeof field.values === 'object'
            ? field.values
            : Array(1).fill((field as any)?.value)

        return (
          <select
            id={field.name}
            name={field.name}
            required={field.required}
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:#cecece"
          >
            {optionArray?.map((option, j) => (
              <option key={j} value={option?.value} selected={option?.selected}>
                {option?.label ?? option?.value}
              </option>
            ))}
          </select>
        )

      case 'checkbox-group':
        return (
          <div className="space-y-2">
            {field.values?.map((option, j) => (
              <div key={j} className="flex items-center">
                <input
                  id={`${field.name}-${option.value}`}
                  type="checkbox"
                  name={field.name}
                  value={option.value}
                  defaultChecked={option.selected}
                  className="mr-2"
                />
                <label
                  htmlFor={`${field.name}-${option.value}`}
                  className="text-gray-700"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        )

      case 'radio-group':
        return (
          <div className="space-y-2">
            {field.values?.map((option, j) => (
              <div key={j} className="flex items-center">
                <input
                  id={`${field.name}-${option.value}`}
                  type="radio"
                  name={field.name}
                  value={option.value}
                  defaultChecked={option.selected}
                  className="mr-2"
                />
                <label
                  htmlFor={`${field.name}-${option.value}`}
                  className="text-gray-700"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        )

      case 'textarea':
        return (
          <textarea
            id={field.name}
            name={field.name}
            required={field.required}
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:#cecece"
            placeholder={field.placeholder}
          >
            {field.value}
          </textarea>
        )

      case 'date':
        return (
          <input
            id={field.name}
            type="date"
            name={field.name}
            value={field.value}
            required={field.required}
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:#cecece"
          />
        )

      case 'number':
        return (
          <input
            id={field.name}
            type="number"
            name={field.name}
            required={field.required}
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:#cecece"
            min={field.min}
            max={field.max}
            step={field.step}
            value={field.value}
          />
        )

      case 'text':
        return (
          <input
            id={field.name}
            type="text"
            name={field.name}
            value={field.value}
            required={field.required}
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:#cecece"
            placeholder={field.placeholder}
          />
        )

      case 'button':
        return (
          <button type="submit" className={`btn ${field.className}`}>
            {field.label}
          </button>
        )

      case 'file':
        return (
          <div>
            <input
              id={field.name}
              type="file"
              name={field.name}
              required={field.required}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:#cecece"
              onChange={(e) => handleFileChange(e, field.name)}
              disabled={isFileUploading[field.name]}
            />
            {isFileUploading[field.name] && (
              <p className="text-sm text-blue-600 mt-1">Uploading...</p>
            )}
            {uploadedFiles[field.name] && (
              <p className="text-sm text-green-600 mt-1">
                File uploaded successfully!
              </p>
            )}
          </div>
        )

      case 'header':
        return <h1 className="text-2xl font-bold mb-4">{field.label}</h1>

      case 'paragraph':
        return <p className="text-gray-700 mb-4">{field.label}</p>

      default:
        return null
    }
  }

  return (
    <form onSubmit={onSubmit} className="rounded-lg border-2 p-2">
      {data?.map((field: Field, index: number) => (
        <div key={index} className="mb-2">
          {field.type != 'button' && (
            <label
              htmlFor={field.name}
              className="block mb-2 font-semibold text-sm text-gray-700"
            >
              {field.label}
            </label>
          )}
          {renderField(field)}
        </div>
      ))}
    </form>
  )
}

DynamicForm.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      label: PropTypes.string,
      type: PropTypes.oneOf([
        'select',
        'checkbox-group',
        'radio-group',
        'textarea',
        'date',
        'number',
        'text',
        'button',
        'file',
        'header',
        'paragraph',
      ]).isRequired,
      required: PropTypes.bool,
      placeholder: PropTypes.string,
      values: PropTypes.arrayOf(
        PropTypes.shape({
          label: PropTypes.string.isRequired,
          value: PropTypes.string.isRequired,
          selected: PropTypes.bool,
        }),
      ),
      min: PropTypes.number,
      max: PropTypes.number,
      step: PropTypes.number,
      className: PropTypes.string,
      access: PropTypes.bool,
      subtype: PropTypes.string,
    }),
  ).isRequired,
}

export default DynamicForm
