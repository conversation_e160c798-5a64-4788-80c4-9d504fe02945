'use client'
import { useEffect, useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import CustomDatePicker from '../CustomComponents/CustomDatePicker/CustomDatePicker'
import EmailVerify from '../EmailVerify/EmailVerify'
import { getPresignedPutUrl, getPublicUrl } from '@/lib/aws'
import { toast } from 'react-toastify'
import GreenTick from '../Icons/GreenTick'
import MobileLogin from '../MobileLogin/MobileLogin'
import Image from 'next/image'
import Link from 'next/link'
import Signature from '../Signature/Signature'
import { useApi } from '@/hooks/useApi'
import { createTeacherOnboarding } from '@/lib/actions/account.actions'

interface FormData {
  email: string
  w9Form: File | null
  bankAccountNumber: string
  bankName: string
  nameOnAccount: string
  routingNumber: string
}

const TeacherOnboardingForm = ({ userData }: { userData: any }) => {
  const [formData, setFormData] = useState<FormData>({
    email: userData?.email || '',
    w9Form: null,
    bankAccountNumber: '',
    bankName: '',
    nameOnAccount: '',
    routingNumber: '',
  })
  const [w9FileLink, setW9FileLink] = useState<string>('')
  const [signatureUrl, setSignatureUrl] = useState<string>('')
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [recaptchaVerified, setRecaptchaVerified] = useState(false)
  const [isEmailVerified, setIsEmailVerified] = useState(!!userData?.email)
  const [isPhoneVerified, setIsPhoneVerified] = useState(
    !!userData?.phoneNumber,
  )
  const [phoneNumber, setPhoneNumber] = useState(userData?.phoneNumber || '')

  const [isFileUploading, setIsFileUploading] = useState(false)
  const [fileName, setFileName] = useState('')

  const [response, submitForm] = useApi(
    (
      email: string,
      phone: string,
      nameOnAccount: string,
      nameOnBank: string,
      routingNumber: string,
      accountNumber: string,
      w9Url: string,
      signatureUrl: string,
    ) =>
      createTeacherOnboarding(
        email,
        phone,
        nameOnAccount,
        nameOnBank,
        routingNumber,
        accountNumber,
        w9Url,
        signatureUrl,
      ),
    false,
  )

  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Teacher onboarding form submitted successfully.')
    } else if (response.error) {
      toast.error('Error submitting form. Please try again.')
      console.error('Error submitting form:', response.error)
    }
  }, [response])

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target
    if (name === 'routingNumber') {
      const routingValue = value.replace(/\D/g, '')
      if (routingValue.length <= 9) {
        setFormData({ ...formData, [name]: routingValue })
      }
      return
    }

    if (name === 'bankAccountNumber') {
      const accountValue = value.replace(/\D/g, '')
      setFormData({ ...formData, [name]: accountValue })
      return
    }

    setFormData({ ...formData, [name]: value })

    if (errors[name]) {
      setErrors({ ...errors, [name]: '' })
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setIsFileUploading(true)
    try {
      const timeStamp = new Date().getTime()
      const fileName = `w9_form_${timeStamp}_${file.name}`

      const presignedUrl = await getPresignedPutUrl(fileName, file.type)
      const uploadResponse = await fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      })

      if (uploadResponse.ok) {
        const w9Link = await getPublicUrl(fileName)
        setW9FileLink(w9Link)
        setFileName(file.name)
        setFormData({ ...formData, w9Form: file })
        toast?.success('W9 form uploaded successfully!', { autoClose: 1500 })
      } else {
        throw new Error('Upload failed')
      }
    } catch (error) {
      console.error('Error uploading W9 form:', error)
      toast?.error('Error uploading W9 form. Please try again.', {
        autoClose: 1500,
      })
    } finally {
      setIsFileUploading(false)
    }
  }

  const handleSignatureComplete = (signatureDataUrl: string) => {
    setSignatureUrl(signatureDataUrl)
  }

  const uploadSignatureToS3 = async (
    canvas: HTMLCanvasElement,
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      canvas.toBlob(async (blob) => {
        if (!blob) {
          reject(new Error('Failed to create signature blob'))
          return
        }

        try {
          const timeStamp = new Date().getTime()
          const fileName = `signature_${timeStamp}.png`

          const presignedUrl = await getPresignedPutUrl(fileName, 'image/png')
          const uploadResponse = await fetch(presignedUrl, {
            method: 'PUT',
            body: blob,
            headers: {
              'Content-Type': 'image/png',
            },
          })

          if (uploadResponse.ok) {
            const signatureLink = await getPublicUrl(fileName)
            resolve(signatureLink)
          } else {
            throw new Error('Signature upload failed')
          }
        } catch (error) {
          reject(error)
        }
      }, 'image/png')
    })
  }

  const handleButtonClick = async () => {
    try {
      let finalSignatureUrl = signatureUrl

      if (signatureUrl && signatureUrl.startsWith('data:')) {
        const signatureCanvas = document.querySelector(
          'canvas',
        ) as HTMLCanvasElement
        if (signatureCanvas) {
          finalSignatureUrl = await uploadSignatureToS3(signatureCanvas)
        }
      }
      await submitForm(
        formData.email,
        phoneNumber,
        formData.nameOnAccount,
        formData.bankName,
        formData.routingNumber,
        formData.bankAccountNumber,
        w9FileLink,
        finalSignatureUrl,
      )

      toast.success(
        'Teacher onboarding form submitted successfully! You will receive a confirmation email shortly.',
      )

      setFormData({
        email: '',
        w9Form: null,
        bankAccountNumber: '',
        bankName: '',
        nameOnAccount: '',
        routingNumber: '',
      })
      setRecaptchaVerified(false)
      setIsEmailVerified(false)
      setFileName('')
      setW9FileLink('')
      setSignatureUrl('')
    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error('Error submitting form. Please try again.')
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Teacher Welcome Aboard!!!</h1>

      <div className="mb-6">
        <p className="text-gray-600 mb-4">
          Please provide your details below to start the onboarding process. All
          fields marked with * are required.
        </p>
      </div>
      <div className="space-y-6">
        <div>
          <EmailVerify
            user={null}
            email={formData.email}
            setEmail={(email: string) => setFormData({ ...formData, email })}
            phoneNumber={''}
            studentOnBoarding={false}
            setIsEmailVerified={setIsEmailVerified}
            isEmailVerified={isEmailVerified}
            title="Email"
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
          {errors.emailVerification && (
            <p className="text-red-500 text-sm mt-1">
              {errors.emailVerification}
            </p>
          )}
        </div>
        <p className="text-sm text-gray-600">
          This email will be attached to your muse account.
        </p>

        {isPhoneVerified ? (
          <div className="space-y-1">
            <label htmlFor="phoneNumber" className="block text-sm font-medium">
              Phone
            </label>
            <div className="relative">
              <input
                type="text"
                value={phoneNumber}
                readOnly={true}
                className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                placeholder="Enter your number"
                required
              />
              <span className="absolute right-3 top-3 text-sm font-semibold text-color-green-4 flex items-center gap-2 cursor-pointer">
                <GreenTick />
                Verified
              </span>
            </div>
          </div>
        ) : (
          <div className="teacher-onboarding">
            <MobileLogin
              showOtpBtn={false}
              setIsPhoneVerified={setIsPhoneVerified}
              setUserPhone={setPhoneNumber}
              title="Phone Number"
            />
          </div>
        )}

        <div>
          <p className="text-gray-700 mb-4">
            Please complete and upload a W9 Form below. Find the blank form
            here: <br />
            <a
              href="https://www.irs.gov/pub/irs-pdf/fw9.pdf"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 font-medium hover:text-blue-800 underline"
            >
              https://www.irs.gov/pub/irs-pdf/fw9.pdf
            </a>
          </p>

          <label className="block mb-1 text-sm font-medium">
            W9 <span className="text-red-500">*</span>
          </label>
          <div className="border border-gray-300 rounded p-4 flex flex-col items-center justify-center relative h-32">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-10 w-10 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
            <p className="mt-2 text-sm text-gray-500">
              {isFileUploading
                ? 'Uploading...'
                : fileName
                  ? fileName
                  : 'Click or drag a file to this area to upload.'}
            </p>
            <input
              type="file"
              accept=".pdf"
              onChange={handleFileChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              required
            />
          </div>
          {errors.w9Form && (
            <p className="text-red-500 text-sm mt-1">{errors.w9Form}</p>
          )}
        </div>

        <div className="border-t pt-6">
          <h2 className="text-xl font-semibold">
            DIRECT DEPOSIT AUTHORIZATION
          </h2>
          <p className="mb-2 mt-1 text-color-grey-1 text-base">
            Please print and complete ALL the information below
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="nameOnAccount"
                className="block mb-1 text-sm font-medium"
              >
                Name on Account <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="nameOnAccount"
                name="nameOnAccount"
                placeholder="Enter account holder name"
                value={formData.nameOnAccount}
                onChange={handleInputChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.nameOnAccount && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.nameOnAccount}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="bankName"
                className="block mb-1 text-sm font-medium"
              >
                Name on Bank <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="bankName"
                name="bankName"
                placeholder="Enter bank name"
                value={formData.bankName}
                onChange={handleInputChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.bankName && (
                <p className="text-red-500 text-sm mt-1">{errors.bankName}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="routingNumber"
                className="block mb-1 text-sm font-medium"
              >
                9 Digit Routing Number <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="routingNumber"
                name="routingNumber"
                placeholder="Enter 9-digit routing number"
                value={formData.routingNumber}
                onChange={handleInputChange}
                required
                maxLength={9}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.routingNumber && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.routingNumber}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="bankAccountNumber"
                className="block mb-1 text-sm font-medium"
              >
                Bank Account Number <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="bankAccountNumber"
                name="bankAccountNumber"
                placeholder="Enter account number"
                value={formData.bankAccountNumber}
                onChange={handleInputChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.bankAccountNumber && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.bankAccountNumber}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="w-full flex flex-col justify-start">
          <Image
            width={500}
            height={500}
            src="/account-passbook.png"
            alt="Bank Information"
            className="max-w-full h-auto"
          />
          <h2 className="font-medium">
            Image needs to be added after bank name and before A/C number and 9
            digit routing number
          </h2>
        </div>

        <p className="text-base text-color-grey-3">
          <strong>The Muse Writers Center</strong> is hereby authorized to
          directly deposit my pay to the account listed above. This
          authorization will remain in effect until I modify or cancel it in
          writing.
        </p>

        <div>
          <Link
            href={
              'https://the-muse.org/wp-content/uploads/2025/02/2025-Idependent-Contractor-Contract.pdf'
            }
            target="_blank"
            className="text-blue-600 font-medium hover:text-blue-800 underline"
          >
            Download and read independent contractor contract
          </Link>
        </div>

        <div>
          <Signature onSignatureComplete={handleSignatureComplete} />
          {errors.signature && (
            <p className="text-red-500 text-sm mt-1">{errors.signature}</p>
          )}
        </div>

        <div className="mt-6">
          <CustomButton
            title="Submit Onboarding"
            isLoading={response.isFetching}
            onClick={handleButtonClick}
            height={12}
          />
        </div>
      </div>
    </div>
  )
}

export default TeacherOnboardingForm
