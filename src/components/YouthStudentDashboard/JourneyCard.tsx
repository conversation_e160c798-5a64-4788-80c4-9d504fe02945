'use client'

import { useApi } from '@/hooks/useApi'
import { getAccountStats } from '@/lib/actions/account.actions'
import { useEffect } from 'react'
import clsx from 'clsx'
import {
  selectYouthMuseJourney,
  setYouthMuseJourney,
  useDispatch,
  useSelector,
} from '@/lib/redux'

const StatCard = ({
  count,
  label,
  bgColor,
  textColor,
}: {
  count: string
  label: string
  bgColor: string
  textColor: string
}) => (
  <div
    className={clsx(
      'flex flex-col items-center px-4 py-6 rounded-2xl flex-1',
      bgColor,
    )}
  >
    <div className={clsx('text-[48px] font-bold leading-[56px]', textColor)}>
      {count}
    </div>
    <div className="text-base font-semibold text-color-black-2/60 mt-1 text-center">
      {label}
    </div>
  </div>
)

const JourneyShimmer = () => (
  <div className="p-5 bg-white rounded-2xl shadow-sm animate-pulse">
    <div className="flex gap-4">
      {Array(4)
        .fill(0)
        .map((_, i) => (
          <div key={i} className="flex-1 h-[120px] rounded-2xl bg-gray-200" />
        ))}
    </div>
  </div>
)

const YouthStudentJourney = () => {
  const [classStats, fetchClassStats] = useApi(
    (accessToken: string, dashboardType: 'Adult Student' | 'Youth Student') =>
      getAccountStats(accessToken, dashboardType),
  )
  const dispatch = useDispatch()
  const cachedJourney = useSelector(selectYouthMuseJourney)

  useEffect(() => {
    if (!cachedJourney || Object.keys(cachedJourney).length === 0) {
      fetchClassStats('Youth Student')
    }
  }, [])

  useEffect(() => {
    if (classStats.isSuccess) {
      dispatch(setYouthMuseJourney(classStats?.data?.data))
    }
  }, [classStats])

  const stats =
    cachedJourney && Object.keys(cachedJourney).length > 0
      ? cachedJourney
      : classStats?.data?.data

  if (classStats?.isFetching) {
    return <JourneyShimmer />
  }

  return (
    <div className="p-5 bg-neutral-white rounded-2xl shadow-sm">
      <h2 className="font-semibold text-base text-color-black mb-4">
        Your Muse Journey
      </h2>
      <div className="grid grid-cols-2 xl:grid-cols-4 gap-4 w-full">
        <StatCard
          label="Ongoing Classes"
          count={stats?.ongoingClasses?.toString() || '0'}
          bgColor="bg-color-blue-5/10"
          textColor="text-color-blue-5"
        />
        <StatCard
          label="Classes Taken"
          count={stats?.classesAttended?.toString() || '0'}
          bgColor="bg-color-green-3/10"
          textColor="text-color-green-3"
        />
        <StatCard
          label="Badges"
          count={stats?.badgesEarned?.toString() || '0'}
          bgColor="bg-color-orange/10"
          textColor="text-color-orange"
        />
        <StatCard
          label="Teachers Followed"
          count={stats?.teachersFollowed?.toString() || '0'}
          bgColor="bg-color-purple-2/10"
          textColor="text-color-purple-2"
        />
      </div>
    </div>
  )
}

export default YouthStudentJourney
