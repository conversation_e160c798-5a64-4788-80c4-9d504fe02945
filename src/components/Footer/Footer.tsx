'use client'
import LogoIcon from '@/assets/icons/LogoIcon'
import { NON_LOGGED_IN_PROTECTED_ROUTES } from '@/utils/constants'
import { isHeaderFooterSidebarPath } from '@/utils/utils'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React from 'react'

const Footer = ({ role }: { role?: string }) => {
  const pathname = usePathname()
  const isTwoColumnPath = NON_LOGGED_IN_PROTECTED_ROUTES.includes(
    '/' + pathname.split('/')[1],
  )
  if ((role && !isTwoColumnPath) || isHeaderFooterSidebarPath(pathname))
    return <></>
  return (
    <footer className="py-10 px-4 md:px-12 lg:px-16 bg-black">
      <div className="flex flex-col md:flex-row items-center md:items-start justify-between border-b border-gray-700 pb-10">
        <div className="mb-8 md:mb-0 text-center md:text-left">
          <h2 className="text-lg font-semibold mb-4 text-white">About</h2>
          <ul className="space-y-2">
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                About Us
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Location & Hours
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                People
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                History
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                FAQs
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Policies
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Scholarships
              </Link>
            </li>
          </ul>
        </div>
        <div className="mb-8 md:mb-0 text-center md:text-left">
          <h2 className="text-lg text-white font-semibold mb-4">
            Get Involved
          </h2>
          <ul className="space-y-2">
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Giving & Membership
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Teach at The Muse
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Volunteer at The Muse
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Intern at The Muse
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Job Openings
              </Link>
            </li>
            <li>
              <Link href="#" className="text-gray-300 hover:text-white">
                Annual Fundraisers
              </Link>
            </li>
          </ul>
        </div>

        <div className="mb-8 md:mb-0 text-white text-center md:text-left">
          <h2 className="text-lg font-semibold mb-4">Connect</h2>
          <div className="mb-4">
            <p>The Muse Writers Center</p>
            <p>2200 Colonial Ave, Suite #3</p>
            <p>Norfolk, Virginia (VA) 23517</p>
          </div>
          <div className="mb-4">
            <p>757-818-9880</p>
          </div>
          <div>
            <Link
              href="mailto:<EMAIL>"
              className="text-gray-300 hover:text-white"
            >
              <EMAIL>
            </Link>
          </div>
        </div>
        <div className="mb-8 md:mb-0 text-white text-center md:text-left">
          <h2 className="text-lg font-semibold mb-4">Get In Touch</h2>
          <p className="mb-4">
            Stay up-to-date with our
            <br />
            latest news and updates.
          </p>
          <div className="flex space-x-2 mb-4 justify-center md:justify-start">
            <Link
              href="#"
              className="bg-yellow-500 text-black p-2 rounded-md hover:bg-yellow-400"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
              </svg>
            </Link>
            <Link
              href="#"
              className="bg-yellow-500 text-black p-2 rounded-md hover:bg-yellow-400"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" />
              </svg>
            </Link>
            <Link
              href="#"
              className="bg-yellow-500 text-black p-2 rounded-md hover:bg-yellow-400"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
              </svg>
            </Link>
          </div>
        </div>
        <div>
          <LogoIcon />
        </div>
      </div>

      <div className="pt-8 flex flex-col md:flex-row justify-between items-start md:items-center">
        <div className="flex space-x-4 mb-6 md:mb-0">
          <Link
            href={'/classes'}
            className="bg-white text-black px-6 py-3 rounded-full font-bold hover:bg-gray-200"
          >
            Find a Class
          </Link>
          <Link
            href={'/donations'}
            className="bg-yellow-500 text-black px-6 py-3 rounded-full font-bold flex items-center hover:bg-yellow-400"
          >
            Donate Now
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 ml-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </Link>
        </div>
        <div className="text-gray-400 text-sm">
          © 2025 The Muse Writers Center. All Rights Reserved.
        </div>
      </div>
    </footer>
  )
}

export default Footer
