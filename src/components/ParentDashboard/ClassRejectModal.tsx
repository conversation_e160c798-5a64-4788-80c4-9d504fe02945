'use client'
import { useUpdateParentTodoMutation } from '@/lib/redux/slices/apiSlice/apiSlice'
import { X } from 'lucide-react'
import { useEffect } from 'react'
import { toast } from 'react-toastify'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'

const ClassRejectModal = ({
  onClose,
  item,
}: {
  onClose: () => void
  item: any
}) => {
  const [updateParentTodo, { isLoading, isSuccess, isError }] =
    useUpdateParentTodoMutation()
  const handleReject = async (orderId: string) => {
    const res = await updateParentTodo({
      isTokenRequired: true,
      orderId,
      type: 'Rejected',
    })
  }

  useEffect(() => {
    if (isSuccess) {
      toast.success('Class request rejected successfully', { autoClose: 2000 })
      onClose()
    }
  }, [isSuccess])

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50 p-3">
      <div className="bg-white rounded-2xl shadow-lg max-w-md w-full p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Class Registration Request</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
          >
            <X size={20} className="text-color-grey-1" />
          </button>
        </div>

        {/* Description */}
        <p className="font-normal text-sm text-color-black-4 mb-6">
          {item?.Account__r?.Name} has requested an approval to attend the
          following classes:
        </p>

        <hr className="my-6" />

        <div className="space-y-2 mb-6">
          {item?.orderItems?.records?.map((order: any, index: number) => {
            return (
              <div className="flex justify-between" key={index}>
                <span className="font-bold text-base tracking-normal align-middle">
                  {order?.Product2?.Name}
                </span>
                <span className="font-normal text-base text-color-grey-15 tracking-normal text-right align-middle">
                  ${order?.UnitPrice}
                </span>
              </div>
            )
          })}
        </div>

        <div className="bg-red-100 text-red-800 rounded-lg p-4 mb-6">
          <p className="font-semibold text-red-700">Are you sure?</p>
          <p className="font-normal text-sm text-gray-700 mt-1">
            The student will not be able to register to the class.
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end mt-6 gap-6">
          <CustomButton
            title="Reject"
            onClick={() => handleReject(item?.Id)}
            isLoading={isLoading}
            height={10}
            width="222px"
            backgroundColor="bg-color-yellow"
            classes="text-gray-700 border border-gray-200"
            isDisabled={false}
          />
        </div>
      </div>
    </div>
  )
}

export default ClassRejectModal
