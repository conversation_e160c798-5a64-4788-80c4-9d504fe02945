import { useAuth } from '@/contexts/AuthContext'
import { Mail, Phone } from 'lucide-react'
import Link from 'next/link'
import PenIcon from '../Icons/PenIcon'

const ContactInfoCard = () => {
  const { user } = useAuth()
  return (
    <div className="bg-[#f9f9fb] p-6 rounded-2xl w-full shadow-sm">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-base font-bold text-black">Your Contact Info</h2>
          <p className="text-sm text-color-grey-1 mt-1">
            Ensure we can always reach you with important updates.
          </p>
        </div>
        <Link href="/settings">
          <button className="text-black">
            <PenIcon />
          </button>
        </Link>
      </div>

      <div className="mt-6 space-y-4">
        <div className="flex items-center gap-4">
          <div className="w-10 h-10 flex items-center justify-center bg-orange-100 rounded-full">
            <Mail className="text-orange-500" size={20} />
          </div>
          <div>
            <p className="text-sm text-gray-400">Email</p>
            <p className="text-base font-semibold text-black">{user?.email}</p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="w-10 h-10 flex items-center justify-center bg-green-100 rounded-full">
            <Phone className="text-green-500" size={20} />
          </div>
          <div>
            <p className="text-sm text-gray-400">Phone</p>
            <p className="text-base font-semibold text-black">
              {user?.phoneNumber}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContactInfoCard
