'use client'
import useModal from '@/hooks/useModal'
import React, { useEffect, useState } from 'react'
import TodoListShimmer from '../TeacherTodoListShimmer/TeacherTodoListShimmer'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import {
  useGetParentTodoListQuery,
  useUpdateParentTodoMutation,
} from '@/lib/redux/slices/apiSlice/apiSlice'
import ClassRegistrationModal from '../ClassRegistrationModal/ClassRegistrationModal'
import ClassRejectModal from './ClassRejectModal'

const RegistrationRequests = () => {
  const [showAll, setShowAll] = useState(false)
  const [deletedItems, setDeletedItems] = useState<string[]>([])
  const [updateParentTodo, { isLoading, isSuccess, isError }] =
    useUpdateParentTodoMutation()
  const handleApprove = async (orderId: string, paidBy: 'Parent' | 'Child') => {
    const res = await updateParentTodo({
      isTokenRequired: true,
      orderId,
      type: 'Approved',
      paidBy,
    })
  }

  const handleViewAllButton = () => {
    setShowAll((prev) => !prev)
  }

  const [modal, showModal] = useModal()

  const { data, isFetching } = useGetParentTodoListQuery({
    isTokenRequired: true,
  })

  let visibleItems = !showAll ? data?.data.slice(0, 3) : data?.data

  const handleModal = (item: any, action: string) => {
    showModal({
      title: ``,
      contentFn: (onClose) =>
        action == 'Approve' ? (
          <ClassRegistrationModal
            onClose={onClose}
            item={item}
            setDeletedItems={setDeletedItems}
          />
        ) : (
          <ClassRejectModal onClose={onClose} item={item} />
        ),
      closeOnClickOutside: true,
      size: 'lg',
      rounded: '2xl',
      padding: 0,
      showCloseButton: false,
    })
  }

  const handleApproveRequest = (item: any) => {
    handleModal(item, 'Approve')
  }

  const renderTodoItem = (item: any, index: number) => (
    <div key={index} className="py-3 border-b last:border-none w-full">
      <div className="flex items-center justify-between flex-col xl:flex-row gap-4 w-full">
        <div className="w-full xl:w-[120px] shrink-0 text-sm font-semibold text-color-grey-1 text-center xl:text-start">
          {item?.Account__r?.Name}
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-semibold text-color-black whitespace-normal break-words">
            {item?.orderItems?.records?.length > 1 ? (
              <>
                {item?.orderItems?.records?.length} classes{' '}
                <span className="text-color-grey-1">
                  ({item?.orderItems?.records[0]?.Product2?.Name} +{' '}
                  {item?.orderItems?.records?.length - 1} more)
                </span>
              </>
            ) : (
              item?.orderItems?.records[0]?.Product2?.Name
            )}
          </div>
        </div>

        <div className="w-[100px] shrink-0 text-sm font-semibold text-color-black flex items-center justify-center">
          ${item?.TotalAmount}
        </div>
        <CustomButton
          title="Reject"
          onClick={() => handleModal(item, 'Reject')}
          isLoading={false}
          height={10}
          width="222px"
          backgroundColor="bg-white"
          classes="text-gray-700 border border-gray-200 hover:bg-gray-50"
        />
        <CustomButton
          title="Approve"
          onClick={() => handleApproveRequest(item)}
          isLoading={isLoading}
          height={10}
          width="222px"
          backgroundColor="bg-color-yellow"
          classes="shrink-0 bg-color-yellow px-5 py-[10px] text-color-black font-semibold text-sm rounded-lg"
        />
      </div>
    </div>
  )

  const filteredItems = visibleItems?.filter(
    (item: any) => !deletedItems.includes(item?.Id),
  )

  return (
    <div className="bg-neutral-white rounded-xl p-5 w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base font-semibold leading-6 tracking-normal">
          Class Registration Requests
        </h2>
        {data?.data?.length > 3 && (
          <button
            className="text-xs font-normal leading-[18px] tracking-normal flex items-center gap-1"
            onClick={handleViewAllButton}
          >
            {showAll ? 'View Less' : 'View All'}
          </button>
        )}
      </div>

      <div
        className={showAll ? 'max-h-[320px] overflow-y-auto' : ''}
        style={{ scrollbarWidth: 'none' }}
      >
        {isFetching ? (
          <TodoListShimmer />
        ) : filteredItems?.length > 0 ? (
          filteredItems?.map(renderTodoItem)
        ) : (
          <div className="text-sm text-color-grey-1 text-center py-4 font-medium">
            No items to show
          </div>
        )}
      </div>
      {modal}
    </div>
  )
}

export default RegistrationRequests
