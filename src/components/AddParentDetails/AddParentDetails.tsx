import React, { useEffect, useState } from 'react'
import CustomButton from '@/components/CustomComponents/CustomButton/CustomButton'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import { X } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { addParentAccount } from '@/lib/actions/account.actions'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { updateUserFields } from '@/lib/actions/login.actions'

interface AddParentDetailsProps {
  onClose: () => void
  setParentAccountId: (id: string) => void
}

const AddParentDetails: React.FC<AddParentDetailsProps> = ({
  onClose,
  setParentAccountId,
}) => {
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [email, setEmail] = useState('')
  const [phone, setPhone] = useState('')
  const { setUser } = useAuth()
  const [addParentResponse, addParent] = useApi(
    (
      access: string,
      firstName: string,
      lastName: string,
      email: string,
      phone: string,
    ) => addParentAccount(access, firstName, lastName, email, phone),
  )

  useEffect(() => {
    if (addParentResponse.isSuccess) {
      toast.success('Parent details added successfully')
      setParentAccountId(addParentResponse.data?.data)
      setUser((prevUser: any) => ({
        ...prevUser,
        parent: addParentResponse.data?.data,
      }))
      const data = {
        parent: addParentResponse.data?.data,
      }
      updateUserFields(data)
      onClose()
    }
  }, [addParentResponse])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!firstName || !lastName || !email || !phone) {
      return
    }
    addParent(firstName, lastName, email, '+' + phone)
  }

  return (
    <div className="w-full max-w-md mx-auto rounded-2xl p-4">
      <div className="flex items-center justify-between gap-10 mb-6">
        <h2 className="text-2xl font-bold text-center">
          Add Parent/Guardian Details
        </h2>
        <button
          onClick={onClose}
          className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
        >
          <X size={20} className="text-color-grey-1" />
        </button>
      </div>
      <div className="space-y-5">
        <div>
          <label className="block text-sm font-medium mb-1">First Name</label>
          <input
            type="text"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
            placeholder="Enter first name"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Last Name</label>
          <input
            type="text"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
            placeholder="Enter last name"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Email</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
            placeholder="Enter email"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Mobile</label>
          <PhoneInput
            country={'us'}
            value={phone}
            onChange={setPhone}
            inputStyle={{ width: '100%' }}
            containerStyle={{ marginBottom: '1rem' }}
          />
        </div>
        <CustomButton
          title="Submit Parent Details"
          isLoading={addParentResponse.isFetching}
          height={12}
          onClick={handleSubmit}
        />
      </div>
    </div>
  )
}

export default AddParentDetails
