import Image from 'next/image'
import React from 'react'
import UpCommingClasses from '../UpCommingClasses/UpCommingClasses'
import RightColumnHoc from '../RightColumnHoc/RightColumnHoc'
import EventRegister from '../EventRegister/EventRegister'
import EventClubDetail from '../EventClubDetail/EventClubDetail'

const EventClubDetailPage = ({
  data,
  userData,
  isAlreadyEnrolled,
}: {
  data: any
  userData: any
  isAlreadyEnrolled: boolean
}) => {
  return (
    <div className="w-full flex items-center justify-center">
      <div
        className={`grid grid-cols-1 md:grid-cols-10 px-2 md:px-0  ${userData ? 'w-full md:w-11/12' : 'w-full sm:w-3/4'}`}
      >
        <div className="w-full mb-5 col-span-6">
          {data?.Banner_Image__c && (
            <div className="relative w-full aspect-video rounded-t-xl overflow-hidden">
              <Image
                src={data?.Banner_Image__c}
                alt="User avatar"
                fill
                className="object-cover"
                priority
              />
            </div>
          )}
          <EventClubDetail eventClubData={data} userData={userData} />
          <div className="mt-4 md:mt-7 hidden lg:block">
            <UpCommingClasses title={'Related'} />
          </div>
        </div>
        <div className="col-span-4">
          <RightColumnHoc>
            <EventRegister
              eventClubData={data}
              userData={userData}
              isAlreadyEnrolled={isAlreadyEnrolled}
            />
             <div className="mt-4 md:mt-7 block lg:hidden md:ml-3 lg:ml-0">
              <UpCommingClasses title={'Related'} />
            </div>
          </RightColumnHoc>
        </div>
      </div>
    </div>
  )
}

export default EventClubDetailPage
