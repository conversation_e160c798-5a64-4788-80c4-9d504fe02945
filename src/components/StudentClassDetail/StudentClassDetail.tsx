import Image from 'next/image'
import React from 'react'
import ClassDetailPage from '../ClassDetailPage/ClassDetailPage'
import UpCommingClasses from '../UpCommingClasses/UpCommingClasses'
import RightColumnHoc from '../RightColumnHoc/RightColumnHoc'
import PurchaseClass from '../PurchaseClass/PurchaseClass'

interface StudentClassDetailProps {
  isPrerequisiteSatisfied: boolean
  isAlreadyEnrolled: boolean
  classDetails?: any
  userData?: any
  userBadges?: any
  isApprovalRaised?: boolean
  parentChildren?: any[]
}

const StudentClassDetail = ({
  classDetails,
  userData,
  userBadges,
  isPrerequisiteSatisfied,
  isAlreadyEnrolled,
  isApprovalRaised = false,
  parentChildren,
}: StudentClassDetailProps) => {
  return (
    <div className="w-full flex justify-center">
      {/* container */}
      <div className="grid grid-cols-1 lg:grid-cols-10 w-full lg:w-11/12 xl:w-10/12 px-3 md:px-5 lg:px-0 gap-6">
        {/* Left content (main) */}
        <div className="w-full mb-6 lg:mb-0 lg:col-span-6 space-y-6">
          {classDetails?.Banner_Image__c && (
            <div className="relative w-full aspect-video rounded-t-xl overflow-hidden">
              <Image
                src={classDetails?.Banner_Image__c}
                alt="User avatar"
                fill
                className="object-cover"
                priority
              />
            </div>
          )}

          <ClassDetailPage
            classDetails={classDetails}
            userData={userData}
            userBadges={userBadges?.data?.errors ? [] : userBadges?.data}
            isAlreadyEnrolled={isAlreadyEnrolled}
          />

          <div className="mt-4 md:mt-7 hidden lg:block">
            <UpCommingClasses title={'Related'} />
          </div>
        </div>

        {/* Right column */}
        <div className="w-full lg:col-span-4">
          <RightColumnHoc>
            <PurchaseClass
              classData={classDetails}
              userData={userData}
              isPrerequisiteSatisfied={isPrerequisiteSatisfied}
              isAlreadyEnrolled={isAlreadyEnrolled}
              isApprovalRaised={isApprovalRaised}
              parentChildren={parentChildren}
            />
            <div className="mt-4 md:mt-7 block lg:hidden">
              <UpCommingClasses title={'Related'} />
            </div>
          </RightColumnHoc>
        </div>
      </div>
    </div>
  )
}

export default StudentClassDetail
