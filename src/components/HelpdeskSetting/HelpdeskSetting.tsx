import React from 'react'
import Link from 'next/link'
import RightArrow from '../Icons/RightArrow'

interface SettingItemProps {
  icon: React.ReactNode
  title: string
  description: string
  href: string
}

const HelpCircle = () => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.25977 11.5204V16.4904C4.25977 18.3104 4.25977 18.3104 5.97977 19.4704L10.7098 22.2004C11.4198 22.6104 12.5798 22.6104 13.2898 22.2004L18.0198 19.4704C19.7398 18.3104 19.7398 18.3104 19.7398 16.4904V11.5204C19.7398 9.70043 19.7398 9.70043 18.0198 8.54043L13.2898 5.81043C12.5798 5.40043 11.4198 5.40043 10.7098 5.81043L5.97977 8.54043C4.25977 9.70043 4.25977 9.70043 4.25977 11.5204Z"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.5 8.13V5.5C17.5 3.5 16.5 2.5 14.5 2.5H9.5C7.5 2.5 6.5 3.5 6.5 5.5V8.06"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.6298 11.49L13.1998 12.38C13.2898 12.52 13.4898 12.66 13.6398 12.7L14.6598 12.96C15.2898 13.12 15.4598 13.66 15.0498 14.16L14.3798 14.97C14.2798 15.1 14.1998 15.33 14.2098 15.49L14.2698 16.54C14.3098 17.19 13.8498 17.52 13.2498 17.28L12.2698 16.89C12.1198 16.83 11.8698 16.83 11.7198 16.89L10.7398 17.28C10.1398 17.52 9.67978 17.18 9.71978 16.54L9.77978 15.49C9.78978 15.33 9.70978 15.09 9.60978 14.97L8.93978 14.16C8.52978 13.66 8.69978 13.12 9.32978 12.96L10.3498 12.7C10.5098 12.66 10.7098 12.51 10.7898 12.38L11.3598 11.49C11.7198 10.95 12.2798 10.95 12.6298 11.49Z"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

const Shield = () => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.4902 2.72957L5.50016 4.60957C4.35016 5.03957 3.41016 6.39957 3.41016 7.61957V15.0496C3.41016 16.2296 4.19016 17.7796 5.14016 18.4896L9.44016 21.6996C10.8502 22.7596 13.1702 22.7596 14.5802 21.6996L18.8802 18.4896C19.8302 17.7796 20.6102 16.2296 20.6102 15.0496V7.61957C20.6102 6.38957 19.6702 5.02957 18.5202 4.59957L13.5302 2.72957C12.6802 2.41957 11.3202 2.41957 10.4902 2.72957Z"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 13C13.1046 13 14 12.1046 14 11C14 9.89543 13.1046 9 12 9C10.8954 9 10 9.89543 10 11C10 12.1046 10.8954 13 12 13Z"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 13V16"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

const MessageSquare = () => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2 9C2 5.5 4 4 7 4H17C20 4 22 5.5 22 9V16C22 19.5 20 21 17 21H7"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 9.5L13.87 12C12.84 12.82 11.15 12.82 10.12 12L7 9.5"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 17H8"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 13H5"
        stroke="#FFBB54"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  description,
  href,
}) => (
  <Link
    href={href}
    className="flex  border-2 rounded-2xl p-5 items-center justify-between border-b-1 border-color-grey"
  >
    <div className="flex items-center gap-7">
      <div className="w-12 h-12 flex items-center justify-center rounded-full bg-orange-50">
        {icon}
      </div>
      <div className="flex flex-col gap-3">
        <h3 className="text-sm font-medium">{title}</h3>
        <p className="text-sm text-color-grey-1">{description}</p>
      </div>
    </div>
    <RightArrow />
  </Link>
)

const HelpdeskSetting: React.FC = () => {
  const settings = [
    {
      icon: HelpCircle(),
      title: 'What is Classa',
      description:
        'Here for the first time? See how Classa can help student to improve it self',
      href: '/#',
    },
    {
      icon: Shield(),
      title: 'Term & Condition',
      description:
        'Updating your privacy policy to include Substance, and our own term and privacy policy.',
      href: '/#',
    },
    {
      icon: MessageSquare(),
      title: 'Getting Started',
      description:
        'Everything you need to know to get started with Substance Classa',
      href: '/#',
    },
  ]

  return (
    <div className="flex items-center justify-center flex-col p-6">
      <h1 className="text-lg font-bold text-gray-900 mb-6">Helpdesk</h1>
      <div className="space-y-4 w-3/4">
        {settings.map((setting, index) => (
          <SettingItem
            key={index}
            icon={setting.icon}
            title={setting.title}
            description={setting.description}
            href={setting.href}
          />
        ))}
      </div>
    </div>
  )
}

export default HelpdeskSetting
