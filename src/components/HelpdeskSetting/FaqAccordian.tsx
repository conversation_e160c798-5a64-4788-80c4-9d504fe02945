'use client'
import { useState } from 'react'
import { Minus, Plus } from 'lucide-react'

export default function FAQAccordion({title,faqData}:{title:string,faqData:any}) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null)

  const toggleAccordion = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index)
  }

  return (
    <div className="max-w-2xl  space-y-3">
    <h1 className='font-bold text-xl'>{title}</h1>
      {faqData?.map((faq:any, index:number) => {
        const isOpen = activeIndex === index
        return (
          <div
            key={index}
            className="border rounded-lg overflow-hidden bg-white shadow-sm"
          >
            <button
              onClick={() => toggleAccordion(index)}
              className="w-full flex justify-between items-center p-4 text-left font-medium text-gray-700 hover:bg-gray-50"
            >
                <div className='w-full flex gap-3 items-center justify-between text-base'>
                {faq.question}
              {isOpen ? (
                <Minus className="w-5 h-5 text-gray-700" />
              ) : (
                <Plus className="w-5 h-5 text-gray-700" />
              )}
                </div>
             
            </button>
            {isOpen && (
              <div className="p-4 border-t text-color-grey-1 text-sm">
                {faq.answer}
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}
