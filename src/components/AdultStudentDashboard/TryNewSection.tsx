'use client'
import React, { useEffect, useState } from 'react'
import ClassCard from '../ClassCard/ClassCard'
import Link from 'next/link'
import DashboardClassListShimmer from '../DashboardClassList/DashboardClassListShimmer'
import { useGetClassesQuery } from '@/lib/redux/slices/apiSlice/apiSlice'

const TryNewSection = () => {
  const [classesData, setClassesData] = useState<any[]>([])

  const { data, isFetching } = useGetClassesQuery({
    isTokenRequired: false,
    classType: 'Current',
    classCategory: 'Adult Class',
  })

  useEffect(() => {
    if (data?.success) {
      const classData = data?.data?.data || []
      setClassesData(classData)
    }
  }, [data])

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-medium leading-[1.5] tracking-normal align-middle text-color-black">
          Try Something New
        </h2>
        <Link
          href="/my-bookings"
          className="text-sm font-normal leading-[2] tracking-normal text-right text-color-grey-1"
        >
          View All
        </Link>
      </div>

      {isFetching ? (
        <DashboardClassListShimmer />
      ) : classesData?.length > 0 ? (
        <div className="grid md:grid-cols-2 gap-6">
          {classesData?.slice(0, 2)?.map((item) => (
            <Link href={`/classes/${item?.Id}`} key={item?.Id}>
              <ClassCard
                data={item}
                showRating={false}
                showDetails={true}
                shadow={'sm'}
              />
            </Link>
          ))}
        </div>
      ) : (
        <div className="flex items-center font-bold justify-center mb-10 text-color-grey-1">
          No classes to show
        </div>
      )}
    </div>
  )
}

export default TryNewSection
