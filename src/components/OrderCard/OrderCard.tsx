import React from 'react'
import PurchasedItem from '../PurchasedItem/PurchasedItem'
import { format } from 'date-fns'
import CoinIcon from '../Icons/CoinIcon'
import CardIcon from '../CardIcon/CardIcon'
import Link from 'next/link'

const OrderCard = ({ data }: { data: any }) => {
  return (
    <>
      {data?.map((item: any) => {
        return (
          <div
            className="bg-white rounded-2xl w-full h-max mb-5"
            key={item?.order?.Id}
          >
            <div className="flex justify-between w-full p-5">
              <div className="flex items-center justify-center gap-10">
                <div className="flex flex-col">
                  <h1 className="font-bold text-base">Order Date</h1>
                  <h3 className="text-color-grey-3 font-medium text-sm">
                    {format(item?.order?.CreatedDate, 'dd MMMM yyyy')}
                  </h3>
                </div>
                <div className="flex flex-col">
                  <h1 className="font-bold text-base">Order ID</h1>
                  <h3 className="text-color-grey-3 font-medium text-sm">
                    {item?.order?.OrderNumber}
                  </h3>
                </div>
                <div className="flex flex-col">
                  <h1 className="font-bold text-base">Total</h1>
                  <h3 className="text-color-grey-3 font-medium text-sm">
                    ${item?.totalPrice}.00
                  </h3>
                </div>
              </div>
              <button className="text-color-blue-2 font-semibold text-base">
                Get Invoice
              </button>
            </div>
            <div className="bg-color-grey-2 border w-full"></div>
            <div className="p-5">
              {item?.orderItems?.map((order: any) => {
                if (order?.Product2?.Class__c) {
                  return (
                    <Link
                      href={`/classes/${order?.Product2?.Class__c}`}
                      key={order?.Id}
                    >
                      <PurchasedItem item={order} />
                    </Link>
                  )
                }
              })}
              <div className="border-1 border-dotted w-full"></div>
              <div className="flex items-center justify-between mt-4">
                <div>
                  <div className="mb-4">
                    <h2 className="text-base font-bold">Billing Address</h2>
                    <p className="text-gray-700 font-medium text-sm">
                      {item?.order?.BillingAddress?.street || ''} <br />
                      {(item?.order?.BillingAddress?.city || '') +
                        ' ' +
                        (item?.order?.BillingAddress?.postalCode || '')}
                      <br />
                      {(item?.order?.BillingAddress?.state
                        ? item?.order?.BillingAddress?.state + ', '
                        : '') + (item?.order?.BillingAddress?.country || '')}
                    </p>
                  </div>
                  {item?.payments && item?.payments?.length > 0 && (
                    <div className="">
                      <h2 className="text-base font-bold">Payment Method</h2>
                      <div className="space-y-1">
                        {item?.payments?.map((payment: any) => {
                          return (
                            <div
                              key={payment?.Id}
                              className="text-gray-700 flex items-center mt-2 gap-2 font-medium text-sm"
                            >
                              {payment?.Account_Type__c === 'UserWallet' ? (
                                <>
                                  <div className="w-12 flex justify-center items-center">
                                    <CoinIcon />
                                  </div>
                                  <span className="font-bold">
                                    $ {payment?.Amount__c}
                                  </span>{' '}
                                  paid via
                                  <span className="font-bold">Muse Wallet</span>
                                </>
                              ) : (
                                <>
                                  <CardIcon type={payment?.Account_Type__c} />
                                  <span className="font-bold">
                                    $ {payment?.Amount__c}
                                  </span>{' '}
                                  paid via
                                  <span className="font-bold">
                                    Credit card ending in
                                  </span>
                                  {payment?.Account_Number__c}
                                </>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
                <div className="border-t pt-4 flex flex-col gap-2">
                  <div className="flex justify-between text-gray-700 gap-7 text-base font-semibold">
                    <span>Cart total</span>
                    <span>${Number(item?.totalPrice).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-gray-700 gap-7 font-medium text-sm  ">
                    <p className="w-full">Coupon discount</p>
                    <span>
                      -${Number(item?.order?.Discount__c || 0).toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between  text-black mt-2 gap-7 text-base font-bold">
                    <span>Total amount</span>
                    <span>
                      $
                      {(
                        Number(item?.totalPrice) -
                        Number(item?.order?.Discount__c)
                      ).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      })}
    </>
  )
}

export default OrderCard
