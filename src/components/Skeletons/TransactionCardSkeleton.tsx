import React from 'react'

const TransactionCardSkeleton = ({
  type = 'Credit',
  index = 0,
}: {
  type?: 'All' | 'Credit' | 'Debit'
  index?: number
}) => {
  const getAmountColor = () => {
    if (type === 'All') {
      return index % 2 === 0 ? 'bg-green-300' : 'bg-red-300'
    }
    return type === 'Credit' ? 'bg-green-300' : 'bg-red-300'
  }

  return (
    <div className="flex justify-between items-start py-8 border-b border-color-grey-2 animate-pulse">
      <div className="flex flex-col gap-2">
        <div className="h-8 w-32 bg-gray-300 rounded mb-2"></div>
        <div className="h-5 w-48 bg-gray-200 rounded"></div>
      </div>
      <div className={`h-8 w-24 rounded flex items-center justify-end`}>
        <div className={`h-7 w-20 rounded ${getAmountColor()}`}></div>
      </div>
    </div>
  )
}

export default TransactionCardSkeleton
