'use client'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import Share from '../Icons/Share'
import Person from '../Icons/Person'
import Class from '../Icons/Class'
import Clock from '../Icons/Clock'
import CalendarIcon from '../Icons/CalendarIcon'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { addToCart } from '@/lib/actions/cart.actions'
import { toast } from 'react-toastify'
import { usePathname, useRouter } from 'next/navigation'
import { useApi } from '@/hooks/useApi'
import { formatDateToAmerican, formatSalesforceTime } from '@/utils/utils'
import { useDispatch } from 'react-redux'
import { increaseCartCount } from '@/lib/redux'
import AddToCalendarButton from '../AddToCalendarButton/AddToCalendarButton'
import { EVENT_TYPES } from '@/types/enum'
import { registerForEvent } from '@/lib/actions/class.actions'

const EventClubRegister = ({
  eventClubData,
  userData,
  isAlreadyEnrolled,
}: {
  eventClubData: any
  userData: any
  isAlreadyEnrolled: boolean
}) => {
  const [isAdded, setIsAdded] = useState<boolean>(false)
  const [response, addCart] = useApi(
    (access: string, id: string, amount: number) =>
      addToCart(access, id, amount),
  )
  const [isRegistered, setIsRegistered] = useState<boolean>(isAlreadyEnrolled)
  const [registerEventResponse, eventRegister] = useApi((access: string, id) =>
    registerForEvent(access, id),
  )
  const pathname = usePathname()
  const router = useRouter()
  const dispatch = useDispatch()
  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Successfully added!', { autoClose: 1500 })
      dispatch(increaseCartCount())
      setIsAdded(true)
    }
  }, [response])

  useEffect(() => {
    if (registerEventResponse.isSuccess) {
      setIsRegistered(true)
      if (eventClubData?.Third_party_Registration_Link__c) {
        toast.success('Successfully Registered')
        window.open(eventClubData?.Third_party_Registration_Link__c, '_blank')
      }
    }
  }, [registerEventResponse])

  const handleAddToCart = async () => {
    if (isAlreadyEnrolled) {
      toast.error('You are already enrolled in this class!')
      return
    }
    if (userData) {
      const classCartData = [eventClubData?.Id, Number(eventClubData?.Price__c)]
      await addCart(...classCartData)
    } else {
      toast.error('Please sign in first', { autoClose: 3000 })
      localStorage.setItem('redirect', pathname)
      router.push('/sign-in')
    }
  }

  const handleShare = () => {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL
    navigator.clipboard.writeText(
      baseUrl + '/' + `classes/${eventClubData?.Id}`,
    )
    toast.success('The link has been copied to your clipboard', {
      autoClose: 2500,
    })
  }

  const btntext = useMemo(() => {
    if (eventClubData?.Registration_Required__c) {
      return 'Add to Cart'
    } else {
      if (eventClubData?.Third_party_Registration_Link__c) {
        return 'Register'
      } else {
        return 'I’d like to attend'
      }
    }
  }, [])

  const btnFn = () => {
    if (!userData) {
      toast.info('Please sign-in first')
      router.push('/sign-in')
      return
    }
    if (eventClubData?.Registration_Required__c) {
      return handleAddToCart()
    } else {
      eventRegister(eventClubData?.Id)
    }
  }
  return (
    <div className="p-7 md:ml-3 bg-white rounded-xl w-full">
      {eventClubData?.Category__c && (
        <div className="rounded-md bg-color-yellow-3 px-2 py-1 w-max flex items-center">
          <span className="px-2 py-1 font-bold text-[10px] leading-[15px] text-center align-middle text-color-grey-14">
            {eventClubData?.Category__c}
          </span>
        </div>
      )}
      <div className="flex items-center justify-between gap-3 mt-2">
        <h1 className="text-lg font-bold w-full">
          {eventClubData?.Title__c ?? ''}
        </h1>
        <Share onClick={handleShare} />
      </div>
      <div className="flex items-center justify-start w-full flex-col gap-2 text-sm mt-2">
        <div className="flex items-center gap-3 w-full">
          <div className="flex items-center gap-2">
            <Person />
            {eventClubData?.Registration_Required__c ? (
              <span className="w-max">
                {eventClubData?.Total_Seats__c - eventClubData?.Booked_Seats__c}{' '}
                out of {eventClubData?.Total_Seats__c ?? ''} seats
              </span>
            ) : (
              <span>
                {eventClubData?.Booked_Seats__c <= 0
                  ? 0
                  : eventClubData?.Booked_Seats__c}{' '}
                Going
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2 w-full">
          <CalendarIcon />
          <span className="w-max">
            {formatDateToAmerican(eventClubData?.Start_Date__c)} -{' '}
            {formatDateToAmerican(eventClubData?.End_Date__c)}
          </span>
        </div>
      </div>
      <>
        <div className="my-3 flex gap-4 flex-col">
          {isAdded ? (
            <CustomButton
              isLoading={false}
              title="Go to cart"
              onClick={() => router.push('/cart')}
              height={50}
            />
          ) : (
            <>
              {eventClubData?.Registration_Required__c ||
                (eventClubData?.Third_party_Registration_Link__c && (
                  <CustomButton
                    title={isAlreadyEnrolled ? 'Already Registered' : btntext}
                    isLoading={
                      response.isFetching || registerEventResponse.isFetching
                    }
                    isDisabled={isAlreadyEnrolled || isRegistered}
                    onClick={btnFn}
                    height={50}
                  />
                ))}
            </>
          )}
        </div>
        {eventClubData?.Show_Add_to_Calendar__c && eventClubData?.Rrule__c && (
          <AddToCalendarButton
            title={eventClubData?.Title__c}
            rrule={eventClubData?.Rrule__c}
          />
        )}
      </>
    </div>
  )
}

export default EventClubRegister
