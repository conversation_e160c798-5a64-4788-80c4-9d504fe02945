import React, { useEffect, useMemo, useState } from 'react'
import useModal from '@/hooks/useModal'
import PricingToggle from '../Subscription/PricingToggle'
import MembershipPlanModal from './MembershipPlanModal'
import { useApi } from '@/hooks/useApi'
import { getMembershipProducts } from '@/lib/actions/membership.actions'
import MembershipPlansSkeleton from '../Skeleton/MembershipPlansSkeleton'
import MembershipCard from '../MembershipCard/MembershipCard'

const MembershipPlans = () => {
  const [billing, setBilling] = useState<'monthly' | 'annual'>('monthly')
  const [modal, showModal] = useModal()
  const [plans, setPlans] = useState<any>([])
  const [activePlan, setActivePlan] = useState<any>(null)
  const [response, fetchPlans] = useApi((access: string) =>
    getMembershipProducts(access),
  )

  useEffect(() => {
    if (response.isSuccess) {
      const plans = response?.data?.data?.productRecords || []
      // Sort plans by selected billing price
      const sortedPlans = [...plans].sort((a, b) => {
        const priceA =
          billing === 'monthly' ? a?.Monthly_Minimum__c : a?.Yearly_Minimum__c
        const priceB =
          billing === 'monthly' ? b?.Monthly_Minimum__c : b?.Yearly_Minimum__c
        return (priceA || 0) - (priceB || 0)
      })
      const currentActivePlan = response?.data?.data?.activeMembership
      const structuredPlan = sortedPlans.map((plan: any, index: number) => {
        return {
          id: plan.Id,
          name: plan.Name,
          price: {
            monthly: plan?.Monthly_Minimum__c,
            annual: plan?.Yearly_Minimum__c,
          },
          description: plan?.Description || '',
          current: plan.Id == currentActivePlan?.Product__r?.Id,
          isNext:
            sortedPlans[index - 1]?.Id === currentActivePlan?.Product__r?.Id,
        }
      })
      setPlans(structuredPlan)
      setActivePlan(response?.data?.data?.activeMembership)
    }
  }, [response])

  useEffect(() => {
    fetchPlans()
  }, [])

  const showAnnual = useMemo(() => {
    let yearlySaving = {
      percentage: 0,
      show: false,
    }
    plans.forEach((product: any) => {
      if (Number(product.annualSavings) > 0) {
        yearlySaving.percentage = Math.max(
          yearlySaving.percentage,
          Number(product.annualSavings),
        )
        yearlySaving.show = true
      }
    })
    return yearlySaving
  }, [plans])

  const handleModal = () => {
    showModal({
      title: '',
      size: 'lg',
      rounded: '2xl',
      closeOnClickOutside: true,
      showCloseButton: false,
      contentFn: (onClose) => (
        <MembershipPlanModal onClose={onClose} plans={plans} />
      ),
    })
  }
  return response.isFetching ? (
    <MembershipPlansSkeleton />
  ) : (
    <div className="bg-white rounded-xl p-8">
      <h2 className="text-2xl font-bold mb-6">
        {activePlan ? 'Muse Member' : 'Become a Muse Member'}
      </h2>
      <div className="mb-8">
        <PricingToggle
          billing={billing}
          onChangeBilling={setBilling}
          showAnnual={showAnnual}
        />
      </div>
      <div className="flex flex-col gap-6">
        {plans.map((plan: any, idx: number) => {
          return (
            <MembershipCard
              key={plan.id}
              plan={plan}
              handleModal={handleModal}
              billing={billing}
            />
          )
        })}
      </div>
      {modal}
    </div>
  )
}

export default MembershipPlans
