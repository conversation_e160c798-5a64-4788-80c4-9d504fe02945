import { ROLES } from '@/types/enum'
import DashboardClassList from '../DashboardClassList/DashboardClassList'
import DashboardMailingList from '../DashboardMailingList/DashboardMailingList'
import PageHeader from '../PageHeader/PageHeader'
import QuotesCard from '../QuotesCard/QuotesCard'
import RightColumnHoc from '../RightColumnHoc/RightColumnHoc'
import TeacherDashboardEvents from '../TeacherDashboardEvents/TeacherDashboardEvents'
import TeacherTodoList from '../TeacherTodoList/TeacherTodoList'
import TeacherJour<PERSON> from './TeacherJourney'
import TeacherQuoteCard from './TeacherQuoteCard'

const TeacherDashboard = ({ userData }: { userData: any }) => {
  return (
    <div className="flex flex-col sm:flex-row w-screen md:w-full">
      <div className="w-full flex-1">
        {userData && (
          <PageHeader
            heading={`Hey, ${userData?.name}!`}
            backgroundColor="#F5F5F7"
            showMessage={false}
            showSearch={false}
            showCart={userData?.role != ROLES.Teacher}
            userData={userData}
          />
        )}
        <div className="flex flex-col gap-5 p-3 md:pr-0">
          <QuotesCard type="Teacher" />
          <TeacherJourney />
          <div className="flex flex-col items-center gap-3 mt-5 ">
            <TeacherTodoList />
          </div>
          <div className="mt-5">
            <DashboardClassList selfClasses={true} />
          </div>
          <div className="mt-5">
            <DashboardMailingList />
          </div>
        </div>
      </div>
      <div className="p-3 md:min-w-96 p-3 shrink-0">
        <RightColumnHoc>
          <TeacherDashboardEvents />
        </RightColumnHoc>
      </div>
    </div>
  )
}

export default TeacherDashboard
