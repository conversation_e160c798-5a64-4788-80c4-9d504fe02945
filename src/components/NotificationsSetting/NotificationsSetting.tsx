"use client";
import { useAuth } from "@/contexts/AuthContext";
import { useApi } from "@/hooks/useApi";
import { updateAccountById } from "@/lib/actions/account.actions";
import { ROLES } from "@/types/enum";
import React, { FormEvent, useEffect, useState } from "react";
import { toast } from "react-toastify";
import CustomButton from "../CustomComponents/CustomButton/CustomButton";

interface FormData {
  emailNotifications: boolean
}

const NotificationsSettings = ({ userData }: { userData: any }) => {

  const { user } = useAuth()
  const [formData, setFormData] = useState<FormData>({
    emailNotifications: false,
  })

  const [response, updateDetails] = useApi(
    (access: string, data: any) => updateAccountById(access, data),
    true,
  )

  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Notification settings updated successfully')
    }
  }, [response])

  useEffect(() => {
    if (userData) {
      setFormData((prev) => ({
        ...prev,
        emailNotifications: userData.emailNotificationsAllowed || false,
      }))
    }
  }, [userData])

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (userData && userData?.id) {
      const updateData = {
        emailNotificationsAllowed: String(formData.emailNotifications),
      }
      updateDetails(updateData)
    }
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }))
  }



  return (
    <div className="flex flex-col gap-5">
      <h1 className="text-lg font-bold">Notifications Settings</h1>

      {user?.role !== ROLES.Teacher && (
        <div className="space-y-1">
          <div className="flex items-start space-x-3">
            <input
              type="checkbox"
              id="emailNotifications"
              name="emailNotifications"
              checked={formData.emailNotifications}
              onChange={handleCheckboxChange}
              className="mt-1 h-4 w-4 text-color-yellow focus:ring-color-yellow border-gray-300 rounded"
            />
            <div className="flex flex-col">
              <label
                htmlFor="emailNotifications"
                className="text-sm font-medium text-gray-900 cursor-pointer"
              >
                Enable Email Notifications
              </label>
              <p className="text-xs text-color-grey-6 mt-1">
                Receive email updates about class schedules, announcements,
                and important notifications from your teachers.
              </p>
            </div>
          </div>
        </div>
      )}

      <CustomButton
        title="Update"
        isLoading={response.isFetching}
        onClick={handleSubmit}
        width="w-20"
      />
    </div>
  );
};

export default NotificationsSettings;
