'use client'
import React, { useEffect, useState } from 'react'
import styles from './NotificationsSetting.module.css'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useApi } from '@/hooks/useApi'
import { updateAccountById } from '@/lib/actions/account.actions'
import { toast } from 'react-toastify'

const NotificationsSettings = ({ userData }: { userData: any }) => {
  const [formData, setFormData] = useState({
    emailNotifications: true,
  })

  const [response, updateDetails] = useApi(
    (access: string, data: any) => updateAccountById(access, data),
    true,
  )

  useEffect(() => {
    if (userData) {
      setFormData((prev) => ({
        ...prev,
        emailNotifications: userData.emailNotificationsAllowed || false,
      }))
    }
  }, [userData])

  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Notification updated successfully')
    }
  }, [response])

  const handleSubmit = async (e: any) => {
    e.preventDefault()
    if (userData && userData?.id) {
      const updateData = {
        emailNotificationsAllowed: String(formData.emailNotifications),
      }
      updateDetails(updateData)
    }
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }))
  }

  return (
    <div className="">
      <h1 className="text-lg font-bold mb-5">Notification</h1>

      {/* Mobile Push Notifications */}
      <div className="space-y-1">
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="emailNotifications"
            name="emailNotifications"
            checked={formData.emailNotifications}
            onChange={handleCheckboxChange}
            className="mt-1 h-4 w-4 text-color-yellow focus:ring-color-yellow border-gray-300 rounded"
          />
          <div className="flex flex-col">
            <label
              htmlFor="emailNotifications"
              className="text-sm font-medium text-gray-900 cursor-pointer"
            >
              Enable Email Notifications
            </label>
            <p className="text-xs text-color-grey-6 mt-1">
              Receive email updates about class schedules, announcements, and
              important notifications from your teachers.
            </p>
          </div>
        </div>
        <CustomButton
          title="Update"
          isLoading={response.isFetching}
          onClick={handleSubmit}
        />
      </div>
    </div>
  )
}

export default NotificationsSettings
