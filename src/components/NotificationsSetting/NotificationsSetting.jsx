"use client";
import React, { useState } from "react";
import styles from "./NotificationsSetting.module.css"

const NotificationsSettings = () => {
  const [settings, setSettings] = useState({
    emailNotifications: true,
    emailFrequency: "immediately",
    pushNotifications: "All New Messages",
    updates: {
      tipsAndTricks: true,
      offersAndPromotions: true,
      researchOpportunities: true,
      newsletter: true,
    },
  });


  return (
    <div className="">
      <h1 className="text-lg font-bold mb-5">Notification</h1>

      {/* Mobile Push Notifications */}
      <div className="mb-8 border-b-1 border-color-grey pb-6">
        <h2 className="text-sm font-medium mb-4">Mobile Push Notification</h2>
        <div className="relative w-full max-w-xs">
          <select
            value={settings.pushNotifications}
            onChange={(e) =>
              setSettings({ ...settings, pushNotifications: e.target.value })
            }
            className="w-full p-3 border rounded-lg appearance-none bg-white pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-orange-300"
          >
            <option>All New Messages</option>
            <option>Important Only</option>
            <option>None</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg
              className="w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Email Notifications */}
      <div className="mb-8 border-b-1 border-color-grey pb-6">
        <h2 className="text-sm font-medium mb-2">Email Notifications</h2>
        <p className="text-color-grey-1 text-sm mb-6 w-full md:w-7/12">
          When you&apos;re busy or not online, Substance can send you email
          notifications for any new direct messages or mentions of your name.
        </p>

        <div className="space-y-4">
          <h3 className="text-sm font-medium mb-2">Send Me Email Notifications</h3>

          <div className="space-y-3">
            <div className="flex items-center space-x-1">
              <label htmlFor={`customRadio1`} className={styles.radio__label} key={`radio-1`}>
                <div className={styles.radio__group}>
                  <input type="radio" className={styles.radio__input} id={`customRadio1`} name="customRadio" hidden />
                  <span className={styles.radio__checked}></span>
                </div>
              </label>
              <span className="text-sm text-color-grey-1">
                Send me email notification
              </span>
            </div>

            <div className="flex items-center space-x-1">
              <label htmlFor={`customRadio2`} className={styles.radio__label} key={`radio-2`}>
                <div className={styles.radio__group}>
                  <input type="radio" className={styles.radio__input} id={`customRadio2`} name="customRadio" hidden />
                  <span className={styles.radio__checked}></span>
                </div>
              </label>
              <span className="text-sm text-color-grey-1">Once an hour at most</span>
            </div>

            <div className="flex items-center space-x-1">
              <label htmlFor={`customRadio3`} className={styles.radio__label} key={`radio-3`}>
                <div className={styles.radio__group}>
                  <input type="radio" className={styles.radio__input} id={`customRadio3`} name="customRadio" hidden />
                  <span className={styles.radio__checked}></span>
                </div>
              </label>
              <span className="text-sm text-color-grey-1">Never</span>
            </div>
          </div>
        </div>
      </div>

      {/* Email News & Updates */}
      <div className="mb-8">
        <h2 className="text-sm font-medium mb-2">Email News & Updates</h2>
        <p className="text-color-grey-1 text-sm w-full md:w-7/12 mb-6">
          From time to time, we&apos;d like to send you emails with interesting
          news about Substance and your workspace. You can choose which of these
          updates you&apos;d like to receive:
        </p>

        <div className="space-y-3">
          <label className="flex items-center space-x-3">
            <div className={styles.checkbox}>
              <label htmlFor={`customCheckbox1`} className={styles.checkbox__label} key={`checkbox-1`}>
                <div className={styles.checkbox__group}>
                  <input type="checkbox" className={styles.checkbox__input} id={`customCheckbox1`} hidden />
                  <span className={styles.checkbox__checked}></span>
                </div>
              </label>
            </div>
            <span className="text-sm text-color-grey-1">Tips and Tricks</span>
          </label>

          <label className="flex items-center space-x-3">
            <div className={styles.checkbox}>
              <label htmlFor={`customCheckbox2`} className={styles.checkbox__label} key={`checkbox-2`}>
                <div className={styles.checkbox__group}>
                  <input type="checkbox" className={styles.checkbox__input} id={`customCheckbox2`} hidden />
                  <span className={styles.checkbox__checked}></span>
                </div>
              </label>
            </div>
            <span className="text-sm text-color-grey-1">Offers and Promotions</span>
          </label>

          <label className="flex items-center space-x-3">
            <div className={styles.checkbox}>
              <label htmlFor={`customCheckbox3`} className={styles.checkbox__label} key={`checkbox-3`}>
                <div className={styles.checkbox__group}>
                  <input type="checkbox" className={styles.checkbox__input} id={`customCheckbox3`} hidden />
                  <span className={styles.checkbox__checked}></span>
                </div>
              </label>
            </div>
            <span className="text-sm text-color-grey-1">Research Opportunities</span>
          </label>

          <label className="flex items-center space-x-3">
            <div className={styles.checkbox}>
              <label htmlFor={`customCheckbox4`} className={styles.checkbox__label} key={`checkbox-4`}>
                <div className={styles.checkbox__group}>
                  <input type="checkbox" className={styles.checkbox__input} id={`customCheckbox4`} hidden />
                  <span className={styles.checkbox__checked}></span>
                </div>
              </label>
            </div>
            <span className="text-sm text-color-grey-1">Newsletter</span>
          </label>
        </div>

        <p className="text-color-grey-1 text-sm mt-6">
          If you opt out of the above, note that we&apos;ll still send you important
          administrative emails, such as password resets.
        </p>
      </div>
    </div>
  );
};

export default NotificationsSettings;