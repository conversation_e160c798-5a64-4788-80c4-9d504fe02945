import React from 'react'

const TransactionCard = ({ transaction }: { transaction: any }) => {
  return (
    <div className="mt-5 pb-5 flex items-center flex-col w-full">
    <div className="flex items-center justify-between w-full">
      <h1 className="font-bold text-base mb-1">Booking ID</h1>
      <h1
        className={`${
          transaction.type == 'Credit' ? 'text-color-green' : 'text-color-red-1'
        } text-base text-right font-semibold`}
      >
        {transaction.amount}$
      </h1>
    </div>
  
    <div className="flex items-center justify-between w-full">
      <h3 className="text-xs font-semibold text-color-blue-1">
        {transaction.id}
      </h3>
      <h3 className="text-xs font-semibold text-gray-600">
        {new Date(transaction.createdDate).toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric',
        })}
      </h3>
    </div>
  </div>
  

  )
}

export default TransactionCard
