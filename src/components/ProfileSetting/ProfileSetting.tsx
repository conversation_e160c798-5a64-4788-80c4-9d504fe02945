'use client'
import React, { FormEvent, useEffect, useState } from 'react'
import ProfileUpload from '../ProfileUpload/ProfileUpload'
import { updateAccountById } from '@/lib/actions/account.actions'
import { useApi } from '@/hooks/useApi'
import { StateSelect } from 'react-country-state-city'
import 'react-country-state-city/dist/react-country-state-city.css'
import CustomDatePicker from '../CustomComponents/CustomDatePicker/CustomDatePicker'
import { toast } from 'react-toastify'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useAuth } from '@/contexts/AuthContext'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import { ROLES } from '@/types/enum'
import AddressCard from '../AddressCard/AddressCard'

interface FormData {
  fullName: string
  phoneNumber: string
  bio: string
  about: string
  genre: string
  website: string
  linkedin: string
  instagram: string
  twitter: string
  facebook: string
  showSocial: boolean
  emailNotifications: boolean
}

const ProfileSetting = ({ userData }: { userData: any }) => {
  const { user } = useAuth()
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    phoneNumber: '',
    bio: '',
    about: '',
    genre: '',
    website: '',
    linkedin: '',
    instagram: '',
    twitter: '',
    facebook: '',
    showSocial: false,
    emailNotifications: false,
  })
  const [dateOfBirth, setDateOfBirth] = useState<string>(
    user?.birthday ?? userData?.birthday,
  )

  const [response, updateDetails] = useApi(
    (access: string, data: any) => updateAccountById(access, data),
    true,
  )

  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Profile updated successfully')
    }
  }, [response])

  useEffect(() => {
    if (user) {
      setDateOfBirth(user.birthday)
    }
  }, [user])

  useEffect(() => {
    if (userData) {
      setFormData((prev) => ({
        ...prev,
        fullName: userData.name || '',
        phoneNumber: userData.phoneNumber || '',
        bio: userData.bio || '',
        about: userData.about || '',
        genre: userData.genre || '',
        website: userData.website || '',
        linkedin: userData.linkedin || '',
        instagram: userData.instagram || '',
        twitter: userData.twitter || '',
        facebook: userData.facebook || '',
        emailNotifications: userData.emailNotificationsAllowed || false,
      }))
    }
  }, [userData])

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (userData && userData?.id) {
      const updateData = {
        firstName: formData.fullName.split(' ')[0],
        lastName: formData.fullName.split(' ').slice(1).join(' '),
        dateOfBirth: dateOfBirth,
        bio: formData.bio,
        about: formData.about,
        genre: formData.genre,
        website: formData.website,
        linkedin: formData.linkedin,
        instagram: formData.instagram,
        twitter: formData.twitter,
        facebook: formData.facebook,
        phone: formData.phoneNumber,
        emailNotificationsAllowed: String(formData.emailNotifications),
      }
      updateDetails(updateData)
    }
  }
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handlePhoneChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      phoneNumber: value,
    }))
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }))
  }

  return (
    <div className="w-full">
      <div className="w-full flex items-center justify-center">
        <h1 className="text-base font-bold mb-4 px-3 md:px-0">
          Only <span className="text-color-blue-2 underline">Muse members</span>{' '}
          will have their profiles shown publicly.
        </h1>
      </div>
      <div className="w-full border mb-3 bg-color-grey-2"></div>
      <div className="px-5 md:px-10 pb-5 space-y-6">
        <div className="space-y-1">
          <label htmlFor="bio" className="block text-sm font-medium">
            Profile Picture
          </label>
          <div className="flex items-center gap-5 mb-4">
            <ProfileUpload userData={userData} />
          </div>
        </div>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-1">
            <label htmlFor="fullName" className="block text-sm font-medium">
              Full Name
            </label>
            <input
              type="text"
              id="fullName"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder="Enter your full name"
              required
            />
          </div>
          <CustomDatePicker
            key={dateOfBirth}
            title="Birthday"
            setDateOfBirth={setDateOfBirth}
            dateOfBirth={dateOfBirth}
          />
          <div className="space-y-1">
            <label htmlFor="phoneNumber" className="block text-sm font-medium">
              Phone Number
            </label>
            <PhoneInput
              country={'us'}
              value={formData.phoneNumber}
              onChange={handlePhoneChange}
              inputProps={{
                name: 'parentPhoneNumber',
                required: true,
                className: 'w-full p-2 border border-gray-300 rounded pl-12',
              }}
            />
          </div>

          {user?.role !== ROLES.Teacher && (
            <div className="space-y-1">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="emailNotifications"
                  name="emailNotifications"
                  checked={formData.emailNotifications}
                  onChange={handleCheckboxChange}
                  className="mt-1 h-4 w-4 text-color-yellow focus:ring-color-yellow border-gray-300 rounded"
                />
                <div className="flex flex-col">
                  <label
                    htmlFor="emailNotifications"
                    className="text-sm font-medium text-gray-900 cursor-pointer"
                  >
                    Enable Email Notifications
                  </label>
                  <p className="text-xs text-color-grey-6 mt-1">
                    Receive email updates about class schedules, announcements,
                    and important notifications from your teachers.
                  </p>
                </div>
              </div>
            </div>
          )}
          <div className="space-y-1">
            <label htmlFor="about" className="block text-sm font-medium">
              About
            </label>
            <textarea
              id="about"
              name="about"
              value={formData.about}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              rows={4}
            />
          </div>
          <AddressCard userData={userData} />
          <div className="space-y-1">
            <label htmlFor="genre" className="block text-sm font-medium">
              Genre
            </label>
            <div className="relative">
              <select
                id="genre"
                name="genre"
                value={formData.genre}
                onChange={handleChange}
                className="text-sm w-full p-3 border rounded-lg appearance-none bg-white focus:outline-none focus:ring-2 focus:ring-orange-300"
              >
                <option value="">Select Genre/s</option>
                <option value="fiction">Fiction</option>
                <option value="non-fiction">Non-Fiction</option>
                <option value="poetry">Poetry</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg
                  className="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div className="space-y-1">
            <label htmlFor="website" className="block text-sm font-medium">
              Website
            </label>
            <input
              type="url"
              id="website"
              name="website"
              value={formData.website}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder="https://yourwebsite.com"
            />
          </div>

          <div className="space-y-4">
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="linkedin"
                  className="block text-sm font-medium mb-1"
                >
                  LinkedIn
                </label>
                <input
                  type="url"
                  id="linkedin"
                  name="linkedin"
                  value={formData.linkedin}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://www.linkedin.com/username"
                />
              </div>

              <div>
                <label
                  htmlFor="instagram"
                  className="block text-sm font-medium mb-1"
                >
                  Instagram
                </label>
                <input
                  type="url"
                  id="instagram"
                  name="instagram"
                  value={formData.instagram}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://www.instagram.com/username"
                />
              </div>

              <div>
                <label
                  htmlFor="twitter"
                  className="block text-sm font-medium mb-1"
                >
                  X (Previously Twitter)
                </label>
                <input
                  type="url"
                  id="twitter"
                  name="twitter"
                  value={formData.twitter}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://x.com/username"
                />
              </div>

              <div>
                <label
                  htmlFor="facebook"
                  className="block text-sm font-medium mb-1"
                >
                  Facebook
                </label>
                <input
                  type="url"
                  id="facebook"
                  name="facebook"
                  value={formData.facebook}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://www.facebook.com/username"
                />
              </div>
            </div>
          </div>
          <CustomButton
            title="Update"
            isLoading={response.isFetching}
            onClick={handleSubmit}
          />
        </form>
      </div>
    </div>
  )
}

export default ProfileSetting
