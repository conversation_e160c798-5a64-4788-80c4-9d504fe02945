'use server'
import jsforce from 'jsforce'

import { startOfDay, endOfDay, arrayToSqlInQuery } from '@/utils/utils'
import { connectToSalesforce } from '@/lib/salesforce'
import { responseGenerator } from '@/utils/utils'
import { getRecordTypeByName } from '@/lib/actions/salesforce.actions'
import {
  getPicklistOptionsServer,
  getTeacherForClassServer,
} from '@/lib/actions/account.actions'
import { EVENT_TYPES } from '@/types/enum'
import { getAccountId } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { getAssociatedTeachersForClassByIdServer } from '@/lib/actions/teacher.actions'

export const isRegisteredForClassServer = async (
  userId: string,
  classId: string,
) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id FROM Registration__c WHERE Student__c = '${userId}' AND Class_and_Event__c = '${classId}'`

  const result = (await conn.query(soql)).records

  return result.length > 0
}

export const isClassUnderApprovalServer = async (
  userId: string,
  classId: string,
) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id FROM OrderItem WHERE Order.Parent_Approval_Status__c = 'Pending' AND Account__c = '${userId}' AND Product2.Class__c = '${classId}'`

  const result = (await conn.query(soql)).records

  return result.length > 0
}

export const getAllMeetingsByIdServer = async (
  studentId: string,
  startDate: string,
  endDate: string,
) => {
  const from = startOfDay(startDate)
  const to = endOfDay(endDate)
  const conn = await connectToSalesforce()
  const studentRegisteredClassesIds = await getStudentRegisteredClassIds(
    studentId,
    'All Entities',
  )

  if (studentRegisteredClassesIds.length === 0) {
    return []
  }
  const soql = `SELECT Id, Class_and_Event__r.Id,Class_and_Event__r.Type__c,Class_and_Event__r.Title__c, Class_and_Event__r.Category__c, Starts_On__c,Ends_On__c,Meeting_Time__c, Duration__c from Meeting__c WHERE Ends_On__c >= ${new Date(from).toISOString()} AND Ends_On__c <=${new Date(to).toISOString()} AND Class_and_Event__c IN ${arrayToSqlInQuery(studentRegisteredClassesIds)} ORDER BY Starts_On__c`
  const student_soql = `SELECT Id,Name FROM Account WHERE Id = '${studentId}'`

  const records = (await conn.query(soql)).records
  const student_records = (await conn.query(student_soql)).records

  if (student_records.length === 0) {
    throw new Error('Student record not found')
  }

  const name = student_records[0].Name

  const meetingData = records.map((item) => {
    return {
      classId: item.Class_and_Event__r.Id,
      classTitle: item.Class_and_Event__r.Title__c,
      classCategory: item.Class_and_Event__r.Category__c,
      classType: item.Class_and_Event__r.Type__c,
      meetingDuration: item.Duration__c,
      startDate: item.Starts_On__c,
      meetingTime: item.Meeting_Time__c,
      startTime: item.Starts_On__c,
      endTime: item.Ends_On__c,
      studentName: name,
      studentId: studentId,
    }
  })

  return meetingData
}

export const getClassProductServer = async (classId: string) => {
  const conn = await connectToSalesforce()
  let soql = `SELECT Id from Product2 WHERE Class__c = '${classId}'`

  const classProduct = await conn.query(soql)
  const productId = classProduct.records[0].Id

  return productId!
}

export const getClassPriceServer = async (classId: string) => {
  const conn = await connectToSalesforce()
  let soql = `SELECT Id,Price__c FROM Product2 WHERE Class__c = '${classId}'`

  const classPrice = await conn.query(soql)
  const price = classPrice.records[0].Price__c

  return price
}

export const getClassVenueServer = async (venueId: string) => {
  if (!venueId) {
    return null
  }
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Name, Address__c from Venue__c where Id = '${venueId}'`

  const records = (await conn.query(soql)).records
  const returnVal = {
    name: records[0].Name,
    address: records[0].Address__c,
  }

  return returnVal
}

export const getClassesBadgesByTypeServer = async (
  classId: string,
  type: 'Prerequisite' | 'Award',
) => {
  const conn = await connectToSalesforce()
  let soql = `SELECT Badge__r.Rank__c, Badge__r.Name, Badge__r.Id FROM Class_Badge__c WHERE Parent_Class__c = '${classId}' AND Type__c = '${type}'`

  const data = (await conn.query(soql)).records

  const badges = data.map((item) => {
    return {
      id: item.Badge__r.Id,
      badgeName: item.Badge__r.Name,
      badgeRank: item.Badge__r.Rank__c,
    }
  })

  return badges
}

export const getClassMinimumPriceByIdServer = async (classId: string) => {
  const conn = await connectToSalesforce()
  let soql = `SELECT Flexible_Pricing__c, Minimum_Price__c FROM Class_Event_Club__c WHERE Id = '${classId}'`

  const data = (await conn.query(soql)).records

  if (data.length === 0) {
    throw new Error('data malformed')
  }

  const is_flexible_price = data[0].Flexible_Pricing__c
  const minimum_price = data[0].Minimum_Price__c || 0
  return { is_flexible_price, minimum_price }
}

export const getStudentRegisteredClassIds = async (
  userId: string,
  classCategory: 'All' | 'Adult Class' | 'Youth Class' | 'All Entities',
) => {
  const conn = await connectToSalesforce()
  const recordTypeIds = []

  if (classCategory === 'Adult Class') {
    recordTypeIds.push(
      await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
    )
  } else if (classCategory === 'Youth Class') {
    recordTypeIds.push(
      await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
    )
  } else if (classCategory === 'All Entities') {
    recordTypeIds.push(
      await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
    )
    recordTypeIds.push(
      await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
    )
    recordTypeIds.push(
      await getRecordTypeByName('Event', 'Class_Event_Club__c'),
    )
    recordTypeIds.push(
      await getRecordTypeByName('Outreach', 'Class_Event_Club__c'),
    )
  } else {
    recordTypeIds.push(
      await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
    )
    recordTypeIds.push(
      await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
    )
  }

  const soql = `SELECT Class_and_Event__c FROM Registration__c WHERE Student__c = '${userId}' AND Class_and_Event__r.RecordTypeId IN ${arrayToSqlInQuery(recordTypeIds)} AND Class_and_Event__r.Visible__c = true`

  const records = (await conn.query(soql)).records
  const registeredClassIds = records.map((record) => {
    return record.Class_and_Event__c
  })

  return registeredClassIds
}

export const getClasses = async (
  accessToken: string,
  classType: 'All' | 'Previous' | 'Current',
  classCategory: 'All' | 'Adult Class' | 'Youth Class',
  searchQuery?: string,
  type?: string,
  genre?: string[],
  level?: string[],
  teacherId?: string,
  limit = 20,
  offset = 0,
) => {
  const conn = await connectToSalesforce()
  let query: {
    isDeleted: boolean
    searchQuery?: string
    Type__c?: string
    Genre__c?: string[]
    Level__c?: string[]
    Teacher__c?: string
    RecordTypeIds: string[]
  } = {
    isDeleted: false,
    RecordTypeIds: [],
  }

  let accountRegisteredClassIds = []
  if (accessToken) {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    if (accessTokenUserId) {
      accountRegisteredClassIds = await getStudentRegisteredClassIds(
        accessTokenUserId,
        classCategory,
      )

      if (accountRegisteredClassIds.length === 0) {
        return responseGenerator(true, {
          data: [],
          total: 0,
        })
      }
    }
  }

  if (classCategory === 'Adult Class') {
    query.RecordTypeIds.push(
      await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
    )
  } else if (classCategory === 'Youth Class') {
    query.RecordTypeIds.push(
      await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
    )
  } else {
    query.RecordTypeIds.push(
      await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
    )
    query.RecordTypeIds.push(
      await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
    )
  }

  if (type) {
    query['Type__c'] = type
  }

  if (genre && genre.length) {
    query['Genre__c'] = genre
  }

  if (level && level.length) {
    query['Level__c'] = level
  }

  if (teacherId) {
    query['Teacher__c'] = teacherId
  }

  if (searchQuery && searchQuery.length) {
    query['searchQuery'] = searchQuery
  }

  //write a SOQL query of above query
  let soql = `SELECT Class_and_Event__c FROM Meeting__c  WHERE Class_and_Event__r.RecordTypeId IN ${arrayToSqlInQuery(query.RecordTypeIds)} AND Class_and_Event__r.isDeleted = false AND Class_and_Event__r.Visible__c = true`
  if (query.searchQuery)
    soql += ` AND Class_and_Event__r.Search_Target__c LIKE '%${query.searchQuery.trim().toLowerCase()}%'`
  if (query.Type__c)
    soql += ` AND Class_and_Event__r.Type__c = '${query.Type__c}'`
  if (query.Genre__c)
    soql += ` AND Class_and_Event__r.Genre__c INCLUDES ('${query.Genre__c.join("','")}')`
  if (query.Level__c)
    soql += ` AND Class_and_Event__r.Level__c INCLUDES ('${query.Level__c.join("','")}')`
  if (query.Teacher__c) soql += ` AND Teacher__c = '${query.Teacher__c}'`
  if (classType === 'Previous')
    soql += ` AND Class_and_Event__r.End_Date__c < ${jsforce.Date.TODAY}`
  if (classType === 'Current')
    soql += ` AND Class_and_Event__r.End_Date__c > ${jsforce.Date.TODAY}`

  if (accountRegisteredClassIds.length) {
    soql += ` AND Class_and_Event__r.Id IN ${arrayToSqlInQuery(accountRegisteredClassIds)}`
  }

  if (!accessToken && classType !== 'Previous') {
    soql += `  AND Class_and_Event__r.Available_for_booking__c = true`
  }

  soql += ` GROUP BY Class_and_Event__c`
  const groupedClasses = (await conn.query(soql)).records
  const classIdList = groupedClasses.map((item) => item.Class_and_Event__c)

  if (!classIdList.length) {
    return responseGenerator(true, {
      data: [],
      total: 0,
    })
  }
  soql = `SELECT Id,Name,Title__c,Type__c,Garnish__c,Genre__c,Level__c,Teacher__c,Banner_Image__c,Booked_Seats__c,Total_Seats__c,Total_Meetings_Count__c,Duration__c,Start_Date__c,End_Date__c FROM Class_Event_Club__c WHERE Id IN ${arrayToSqlInQuery(classIdList)}`

  const countSoql = soql
  soql += ` LIMIT ${limit} OFFSET ${offset}`

  const classes = (await conn.query(soql)).records

  const classesWithTeacherAndPrice = await Promise.all(
    classes.map(async (classEvent) => {
      if (!classEvent?.Teacher__c) return { ...classEvent, teacher_data: null }
      const teacher = await getTeacherForClassServer(classEvent.Teacher__c)
      const price = await getClassPriceServer(classEvent.Id as string)
      return { ...classEvent, teacher_data: teacher, Price__c: price }
    }),
  )

  const totalClasses = (await conn.query(countSoql)).records

  let finalClassList = classesWithTeacherAndPrice
  let finalClassesSize = totalClasses.length

  if (accessToken && accountRegisteredClassIds.length === 0) {
    finalClassList = []
    finalClassesSize = 0
  }
  return responseGenerator(true, {
    data: finalClassList,
    total: finalClassesSize,
  })
}

export const getClassById = async (id: string) => {
  const conn = await connectToSalesforce()
  const classEvent = (await conn
    .sobject('Class_Event_Club__c')
    .findOne({
      Id: id,
    })
    .include('Meetings__r')
    .end()) as any

  // Get past meetings with teacher information using separate query
  const pastMeetingsSoql = `Select Id, Starts_On__c,Ends_On__c,Meeting_Time__c,Headcount_based_attendance__c, Attendance_Marked__c, Duration__c, Venue__r.Name,Teacher__r.Id,Teacher__r.Name,Teacher__r.Photo__c from Meeting__c where Class_and_Event__c = '${id}' AND Ends_On__c < ${jsforce.Date.TODAY}`
  const pastMeetings = (await conn.query(pastMeetingsSoql)).records

  // Get upcoming meetings with teacher information using separate query
  const upcomingMeetingsSoql = `Select Id, Starts_On__c,Ends_On__c,Meeting_Time__c,Headcount_based_attendance__c, Attendance_Marked__c, Duration__c, Venue__r.Name,Teacher__r.Id,Teacher__r.Name,Teacher__r.Photo__c from Meeting__c where Class_and_Event__c = '${id}' AND Ends_On__c >= ${jsforce.Date.TODAY}`
  const upcomingMeetings = (await conn.query(upcomingMeetingsSoql)).records

  const soql = `SELECT Id, Name, DeveloperName, IsActive FROM RecordType WHERE Id = '${classEvent?.RecordTypeId}'`
  const recordType = (await conn.query(soql))?.records[0]

  // Add meetings to class event with teacher information
  classEvent.pastMeetings = pastMeetings
  classEvent.upcomingMeetings = upcomingMeetings
  classEvent.recordType = recordType

  classEvent.associatedTeachers =
    await getAssociatedTeachersForClassByIdServer(id)

  const [classPrequisiteBadges, classAwardedBadges, venue] = await Promise.all([
    getClassesBadgesByTypeServer(id, 'Prerequisite'),
    getClassesBadgesByTypeServer(id, 'Award'),
    getClassVenueServer(classEvent.Venue__c),
  ])

  classEvent.prerequisiteBadges = classPrequisiteBadges
  classEvent.awardedBadges = classAwardedBadges
  classEvent.venue = venue

  return responseGenerator(true, classEvent)
}

export const getClassesByDate = async (
  accessToken: string,
  startDate: string,
  endDate: string,
  classCategory: 'All' | 'Adult Class' | 'Youth Class',
  type?: string,
  genre?: string[],
  level?: string[],
) => {
  const from = startOfDay(startDate)
  const to = endOfDay(endDate)

  const recordTypeIds = []

  let accountRegisteredClassIds = []
  if (accessToken) {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    if (accessTokenUserId) {
      accountRegisteredClassIds = await getStudentRegisteredClassIds(
        accessTokenUserId,
        classCategory,
      )
    }

    if (accountRegisteredClassIds.length === 0) {
      return responseGenerator(true, [])
    }
  }
  try {
    const conn = await connectToSalesforce()
    let soql = `SELECT Id,Starts_On__c,Meeting_Time__c,Teacher__c, Venue__r.Name, Duration__c,  Class_and_Event__r.Title__c, Class_and_Event__r.Id,Class_and_Event__r.Banner_Image__c,  Class_and_Event__r.Level__c,Class_and_Event__r.Total_Seats__c, Class_and_Event__r.Booked_Seats__c, Class_and_Event__r.Description__c FROM Meeting__c WHERE Ends_On__c >= ${new Date(from).toISOString()} AND Ends_On__c <= ${new Date(to).toISOString()}`

    if (classCategory === 'Adult Class') {
      recordTypeIds.push(
        await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
      )
    } else if (classCategory === 'Youth Class') {
      recordTypeIds.push(
        await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
      )
    } else {
      recordTypeIds.push(
        await getRecordTypeByName('Youth_Class', 'Class_Event_Club__c'),
      )
      recordTypeIds.push(
        await getRecordTypeByName('Adult_Class', 'Class_Event_Club__c'),
      )
    }

    soql += ` AND Class_and_Event__r.RecordTypeId in ${arrayToSqlInQuery(recordTypeIds)} AND Class_and_Event__r.Visible__c = true`

    if (type) {
      soql += ` AND Class_and_Event__r.Type__c = '${type}'`
    }

    if (level && level.length) {
      soql += ` AND Class_and_Event__r.Level__c INCLUDES ('${level.join("','")}')`
    }

    if (genre && genre.length) {
      soql += ` AND Class_and_Event__r.Genre__c INCLUDES ('${genre.join("','")}')`
    }

    if (accountRegisteredClassIds.length > 0) {
      soql += ` AND Class_and_Event__r.Id IN ${arrayToSqlInQuery(accountRegisteredClassIds)}`
    }

    const classes = (await conn.query(soql)).records

    const classesData = classes.map((item) => {
      return {
        classId: item.Class_and_Event__r.Id,
        teacherId: item.Teacher__c,
        classBannerImage: item.Class_and_Event__r.Banner_Image__c,
        classTitle: item.Class_and_Event__r.Title__c,
        classDescripion: item.Class_and_Event__r.Description__c,
        classLevel: item.Class_and_Event__r.Level__c,
        totalSeats: item.Class_and_Event__r.Total_Seats__c,
        bookedSeats: item.Class_and_Event__r.Booked_Seats__c,
        meetingId: item.Id,
        meetingTime: item.Meeting_Time__c,
        meetingLocation: item.Venue__r?.Name || '',
        meetingDuration: item.Duration__c,
        startDate: item.Starts_On__c,
      }
    })

    const classesWithTeacherAndPrice = await Promise.all(
      classesData.map(async (classEvent) => {
        if (!classEvent?.teacherId) return { ...classEvent, teacher_data: null }
        const teacher = await getTeacherForClassServer(classEvent.teacherId)
        return { ...classEvent, teacher_data: teacher }
      }),
    )

    return responseGenerator(true, classesWithTeacherAndPrice)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error getting classes by date'],
    })
  }
}

export const getClassesTypeFilters = async () => {
  const picklist = await getPicklistOptionsServer('Class_Event_Club__c', 'Type__c')

  return responseGenerator(true, picklist)
}

export const getClassesGenreFilters = async () => {
  const picklist = await getPicklistOptionsServer('Class_Event_Club__c', 'Genre__c')
  return responseGenerator(true, picklist)
}

export const getClassesLevelFilters = async () => {
  const picklist = await getPicklistOptionsServer('Class_Event_Club__c', 'Level__c')
  return responseGenerator(true, picklist)
}

export const getEventCategoryFilters = async () => {
  const picklist = await getPicklistOptionsServer('Class_Event_Club__c', 'Event_Type__c')
  return responseGenerator(true, picklist)
}

export const getEvents = async (
  eventType: 'All' | 'Previous' | 'Current',
  eventCategory?: string[],
  eventSubcategory?: string[],
  searchQuery?: string,
  type?: string,
  teacherId?: string,
  recordType?: 'Event' | 'Outreach' | 'Club' | '',
  limit = 20,
  offset = 0,
) => {
  const conn = await connectToSalesforce()
  let query: {
    isDeleted: boolean
    eventCategory?: string[]
    eventSubcategory?: string[]
    searchQuery?: string
    Type__c?: string
    Genre__c?: string[]
    Teacher__c?: string
    End_Date__c?: any
    classCategory?: string
    RecordTypeId: string[]
  } = {
    isDeleted: false,
    RecordTypeId: [
      await getRecordTypeByName('Event', 'Class_Event_Club__c'),
      await getRecordTypeByName('Outreach', 'Class_Event_Club__c'),
    ],
  }

  if (type) {
    query['Type__c'] = type
  }

  if (teacherId) {
    query['Teacher__c'] = teacherId
  }

  if (eventType === 'Previous') {
    query['End_Date__c'] = {
      $lt: jsforce.SfDate.toDateLiteral(new Date().toISOString()),
    }
  }

  if (eventType === 'Current') {
    query['End_Date__c'] = {
      $gt: jsforce.SfDate.toDateLiteral(new Date().toISOString()),
    }
  }

  if (searchQuery) {
    query['searchQuery'] = searchQuery
  }

  if (eventCategory && eventCategory.length) {
    query['eventCategory'] = eventCategory
  }

  if (eventSubcategory && eventSubcategory.length) {
    query['eventSubcategory'] = eventSubcategory
  }

  if (recordType === 'Event') {
    query.RecordTypeId = [
      await getRecordTypeByName('Event', 'Class_Event_Club__c'),
    ]
  }
  if (recordType === 'Outreach') {
    query.RecordTypeId = [
      await getRecordTypeByName('Outreach', 'Class_Event_Club__c'),
    ]
  }

  if (recordType === 'Club') {
    query.classCategory = 'Club'
  }

  let soql = `SELECT Id, Class_and_Event__c,Class_and_Event__r.Registration_Required__c, Starts_On__c, Ends_On__c, Class_and_Event__r.Garnish__c, Class_and_Event__r.Title__c, Venue__r.Name, Class_and_Event__r.Type__c, Class_and_Event__r.Price__c, Class_and_Event__r.Booked_Seats__c,Class_and_Event__r.Total_Seats__c, Teacher__c, Class_and_Event__r.Banner_Image__c FROM Meeting__c WHERE Class_and_Event__r.RecordTypeId IN ${arrayToSqlInQuery(query.RecordTypeId)} AND Class_and_Event__r.isDeleted = false AND Class_and_Event__r.Visible__c = true AND Class_and_Event__r.Available_for_booking__c = true`
  if (query.searchQuery)
    soql += ` AND Class_and_Event__r.Search_Target__c LIKE '%${query.searchQuery.trim().toLowerCase()}%'`
  if (query.Type__c)
    soql += ` AND Class_and_Event__r.Type__c = '${query.Type__c}'`
  if (query.Teacher__c) soql += ` AND Teacher__c = '${query.Teacher__c}'`
  if (eventType === 'Previous')
    soql += ` AND Ends_On__c < ${jsforce.Date.TODAY}`
  if (eventType === 'Current') soql += ` AND Ends_On__c > ${jsforce.Date.TODAY}`
  if (query.eventCategory) {
    soql += ` AND Class_and_Event__r.Event_Type__c IN ${arrayToSqlInQuery(query.eventCategory)}`
  }
  if (query.eventSubcategory) {
    soql += ` AND Class_and_Event__r.Sub_category__c IN ${arrayToSqlInQuery(query.eventSubcategory)}`
  }
  if (query.classCategory) {
    soql += ` AND Class_and_Event__r.Category__c = '${query.classCategory}'`
  }

  const events = (await conn.query(soql)).records

  const countSoql = soql
  soql += ` LIMIT ${limit} OFFSET ${offset}`
  const eventsWithTeacherAndPrice = await Promise.all(
    events.map(async (event: any) => {
      if (!event?.Teacher__c) return { ...event, teacher_data: null }
      const teacher = await getTeacherForClassServer(event.Teacher__c)
      const price = await getClassPriceServer(
        event.Class_and_Event__c as string,
      )
      return { ...event, teacher_data: teacher, Price__c: price }
    }),
  )

  const totalEvents = (await conn.query(countSoql)).records

  return responseGenerator(true, {
    data: eventsWithTeacherAndPrice,
    total: totalEvents.length,
  })
}

export const getEventById = async (id: string) => {
  try {
    const conn = await connectToSalesforce()
    const classEvent = (await conn
      .sobject('Class_Event_Club__c')
      .findOne({
        Id: id,
      })
      .include('Meetings__r')
      .end()) as any

    // Get past meetings with teacher information using separate query
    const pastMeetingsSoql = `Select Id, Starts_On__c,Ends_On__c,Meeting_Time__c,Headcount_based_attendance__c, Attendance_Marked__c, Duration__c, Venue__r.Name,Teacher__r.Id,Teacher__r.Name,Teacher__r.Photo__c from Meeting__c where Class_and_Event__c = '${id}' AND Ends_On__c < ${jsforce.Date.TODAY}`
    const pastMeetings = (await conn.query(pastMeetingsSoql)).records

    // Get upcoming meetings with teacher information using separate query
    const upcomingMeetingsSoql = `Select Id, Starts_On__c,Ends_On__c,Meeting_Time__c,Headcount_based_attendance__c, Attendance_Marked__c, Duration__c, Venue__r.Name,Teacher__r.Id,Teacher__r.Name,Teacher__r.Photo__c from Meeting__c where Class_and_Event__c = '${id}' AND Ends_On__c >= ${jsforce.Date.TODAY}`
    const upcomingMeetings = (await conn.query(upcomingMeetingsSoql)).records

    const soql = `SELECT Id, Name, DeveloperName, IsActive FROM RecordType WHERE Id = '${classEvent?.RecordTypeId}'`
    const recordType = (await conn.query(soql))?.records[0]

    // Add meetings to class event with teacher information
    classEvent.pastMeetings = pastMeetings
    classEvent.upcomingMeetings = upcomingMeetings
    classEvent.recordType = recordType

    const [venue, associatedTeachers] = await Promise.all([
      getClassVenueServer(classEvent.Venue__c),
      getAssociatedTeachersForClassByIdServer(id),
    ])

    classEvent.associatedTeachers = associatedTeachers
    classEvent.venue = venue

    return responseGenerator(true, classEvent)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getEventsByDate = async (
  startDate: string,
  endDate: string,
  eventCategory: string[],
  type?: string,
  genre?: string[],
  level?: string[],
) => {
  const from = startOfDay(startDate)
  const to = endOfDay(endDate)

  try {
    const conn = await connectToSalesforce()
    let soql = `SELECT Id,Meeting_Time__c, Venue__r.Name, Duration__c,  Class_and_Event__r.Title__c, Class_and_Event__r.Id,Class_and_Event__r.Banner_Image__c,  Class_and_Event__r.Level__c,Class_and_Event__r.Total_Seats__c, Class_and_Event__r.Booked_Seats__c FROM Meeting__c WHERE Ends_On__c >= ${new Date(from).toISOString()} AND Ends_On__c <= ${new Date(to).toISOString()}`
    const recordId = await getRecordTypeByName('Event', 'Class_Event_Club__c')

    soql += ` AND Class_and_Event__r.RecordTypeId = '${recordId}'`

    if (type) {
      soql += ` AND Class_and_Event__r.Type__c = ${type}`
    }

    if (level && level.length) {
      soql += ` AND Class_and_Event__r.Level__c INCLUDES ${arrayToSqlInQuery(level)}`
    }

    if (genre && genre.length) {
      soql += ` AND Class_and_Event__r.Genre__c INCLUDES ${arrayToSqlInQuery(genre)}`
    }

    if (eventCategory && eventCategory.length) {
      soql += ` AND Class_and_Event__r.Event_Type__c IN  ${arrayToSqlInQuery(eventCategory)}`
    }

    const classes = (await conn.query(soql)).records

    const returnData = classes.map((item) => {
      return {
        classId: item.Class_and_Event__r.Id,
        classBannerImage: item.Class_and_Event__r.Banner_Image__c,
        classTitle: item.Class_and_Event__r.Title__c,
        classLevel: item.Class_and_Event__r.Level__c,
        totalSeats: item.Class_and_Event__r.Total_Seats__c,
        bookedSeats: item.Class_and_Event__r.Booked_Seats__c,
        meetingId: item.Id,
        meetingTime: item.Meeting_Time__c,
        meetingLocation: item.Venue__r?.Name || '',
        meetingDuration: item.Duration__c,
        startDate: item.Starts_On__c,
      }
    })
    return responseGenerator(true, returnData)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error getting classes by date'],
    })
  }
}

export const getClubs = async (
  clubType: 'All' | 'Previous' | 'Current',
  limit = 20,
  offset = 0,
) => {
  const conn = await connectToSalesforce()
  let query: {
    isDeleted: boolean
    RecordTypeId: string
    Event_Type__c: EVENT_TYPES.Club
    End_Date__c?: any
  } = {
    isDeleted: false,
    RecordTypeId: await getRecordTypeByName('Event', 'Class_Event_Club__c'),
    Event_Type__c: EVENT_TYPES.Club,
  }

  if (clubType === 'Previous') {
    query['End_Date__c'] = {
      $lt: jsforce.SfDate.TODAY,
    }
  }

  if (clubType === 'Current') {
    query['End_Date__c'] = {
      $gt: jsforce.SfDate.TODAY,
    }
  }

  const clubs = await conn
    .sobject('Class_Event_Club__c')
    .find(query)
    .orderby('CreatedDate', 'DESC')
    .limit(limit)
    .offset(offset)

  const totalClubs = await conn
    .sobject('Class_Event_Club__c')
    .find(query)
    .execute()
  return responseGenerator(true, {
    data: clubs,
    total: totalClubs.length,
  })
}

export const getClubById = async (id: string) => {
  const conn = await connectToSalesforce()
  const event = await conn.sobject('Class_Event_Club__c').retrieve(id)
  const teacher = await getTeacherForClassServer(event.Teacher__c)

  event.teacher_data = teacher
  return responseGenerator(true, event)
}

export const getClubsByDate = async (startDate: string, endDate: string) => {
  const conn = await connectToSalesforce()
  //case 1: startDate and endDate segment is withing the event start and end date segment
  //case 2: the event start and end date segment is within the startDate and endDate segment
  //case 3: the event start date is within the startDate and endDate segment
  //case 4: the event end date is within the startDate and endDate segment
  const classes = await conn
    .sobject('Class_Event_Club__c')
    .find({
      RecordTypeId: await getRecordTypeByName('Event', 'Class_Event_Club__c'),
      Event_Type__c: EVENT_TYPES.Club,
      $or: [
        {
          $and: [
            {
              Start_Date__c: {
                $lte: jsforce.SfDate.toDateLiteral(startDate),
              },
            },
            {
              End_Date__c: {
                $gte: jsforce.SfDate.toDateLiteral(endDate),
              },
            },
          ],
        },
        {
          $and: [
            {
              Start_Date__c: {
                $gte: jsforce.SfDate.toDateLiteral(startDate),
              },
            },
            {
              End_Date__c: {
                $lte: jsforce.SfDate.toDateLiteral(endDate),
              },
            },
          ],
        },
        {
          $and: [
            {
              Start_Date__c: {
                $gte: jsforce.SfDate.toDateLiteral(startDate),
              },
            },
            {
              Start_Date__c: {
                $lte: jsforce.SfDate.toDateLiteral(endDate),
              },
            },
          ],
        },
        {
          $and: [
            {
              End_Date__c: {
                $gte: jsforce.SfDate.toDateLiteral(startDate),
              },
            },
            {
              End_Date__c: {
                $lte: jsforce.SfDate.toDateLiteral(endDate),
              },
            },
          ],
        },
      ],
    })
    .include('Meetings__r')
    .where({
      $and: [
        {
          Meeting_Date__c: {
            $gte: jsforce.SfDate.toDateLiteral(startDate),
          },
        },
        {
          Meeting_Date__c: {
            $lte: jsforce.SfDate.toDateLiteral(endDate),
          },
        },
        {
          isDeleted: false,
          Is_Active__c: true,
        },
      ],
    })
    .end()
  return responseGenerator(true, classes)
}

export const registerForClub = async (accessToken: string, clubId: string) => {
  if (!(await verifyCustomAccountSession(accessToken))) {
    return responseGenerator(false, { errors: ['Unauthorized'] }, true)
  }

  const accessTokenUserId = await getAccountId(accessToken)
  const conn = await connectToSalesforce()

  const clubRecordType = await getRecordTypeByName(
    'Event',
    'Class_Event_Club__c',
  )

  const query = {
    RecordTypeId: clubRecordType,
    Id: clubId,
  }

  const club = await conn.sobject('Class_Event_Club__c').findOne(query)

  if (!club) {
    return responseGenerator(false, { errors: ['Club not found'] })
  }

  const newRegistrationObj = {
    Class_and_Event__c: club.Id,
    Student__c: accessTokenUserId,
  }

  await conn.sobject('Registration__c').create(newRegistrationObj)

  return responseGenerator(true, null)
}

export const registerForEvent = async (
  accessToken: string,
  eventId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const isAlreadyRegistered = await isRegisteredForClassServer(
      accessTokenUserId,
      eventId,
    )
    if (isAlreadyRegistered) {
      throw new Error('Already Registered for event')
    }

    const conn = await connectToSalesforce()

    const eventRecordTypeId = await getRecordTypeByName(
      'Event',
      'Class_Event_Club__c',
    )

    const outReachRecordTypeId = await getRecordTypeByName(
      'Outreach',
      'Class_Event_Club__c',
    )
    const query = {
      Id: eventId,
      $or: [
        {
          RecordTypeId: eventRecordTypeId,
        },
        {
          RecordTypeId: outReachRecordTypeId,
        },
      ],
    }

    const event = await conn.sobject('Class_Event_Club__c').findOne(query)

    if (!event) {
      throw new Error('event not found')
    }

    const newRegistrationObj = {
      Class_and_Event__c: eventId,
      Student__c: accessTokenUserId,
    }
    await conn.sobject('Registration__c').create(newRegistrationObj)
    return responseGenerator(true, null)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getAllMeetings = async (
  accessToken: string,
  startDate: string,
  endDate: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const accessTokenUserId = await getAccountId(accessToken)
    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const meetingData = await getAllMeetingsByIdServer(
      accessTokenUserId,
      startDate,
      endDate,
    )
    return responseGenerator(true, meetingData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getMeetings = async (
  startDate?: string,
  endDate?: string,
  limit = 6,
  offset = 0,
) => {
  try {
    const conn = await connectToSalesforce()

    const from = startDate ? startOfDay(startDate) : new Date()
    const to = endDate ? endOfDay(endDate) : null //null means no end date

    const soql = `SELECT Id,Meeting_Time__c, Duration__c, Venue__r.Name,Starts_On__c, Ends_On__c, Class_and_Event__r.Id,Class_and_Event__r.Title__c from Meeting__c WHERE Ends_On__c >= ${new Date(from).toISOString()} ${to ? `AND Ends_On__c <= ${new Date(to).toISOString()}` : ''} ORDER BY Starts_On__c LIMIT ${limit} OFFSET ${offset}`
    const paginationSoql = `SELECT Id from Meeting__c WHERE Ends_On__c >= ${new Date(from).toISOString()} ${to ? `AND Ends_On__c <= ${new Date(to).toISOString()}` : ''}`
    const meetings = (await conn.query(soql)).records
    const totalMeetings = (await conn.query(paginationSoql)).totalSize

    const meetingsData = meetings.map((meeting: any) => {
      // const startTime = new Date(
      //   `${meeting.Meeting_Date__c}T${meeting.Meeting_Time__c}`,
      // )
      // const endTime = addHours(startTime, meeting.Duration__c)

      return {
        id: meeting.Id,
        meeting_date: meeting.Starts_On__c,
        meeting_start_time: meeting.Starts_On__c,
        meeting_end_time: meeting.Ends_On__c,
        meeting_location: meeting.Venue__r?.Name || '',
        class_id: meeting.Class_and_Event__r.Id,
        class_title: meeting.Class_and_Event__r.Title__c,
      }
    })

    const returnData = {
      data: meetingsData,
      total: totalMeetings,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, { errors: ['Something went wrong'] })
  }
}

export const getSpecials = async (type: 'class' | 'poetry') => {
  try {
    const conn = await connectToSalesforce()
    const soql = `SELECT Id, Class_Event_and_Outreach__r.Id, Class_Event_and_Outreach__r.Genre__c, Class_Event_and_Outreach__r.Banner_Image__c,Class_Event_and_Outreach__r.Available_Seats__c, Class_Event_and_Outreach__r.Booked_Seats__c,Class_Event_and_Outreach__r.Total_Meetings_Count__c, Class_Event_and_Outreach__r.Duration__c, Class_Event_and_Outreach__r.Title__c FROM Class_List_Item__c where Class_List__r.Name = '${type === 'class' ? 'classSpecials' : 'poetrySpecials'}'`

    const records = (await conn.query(soql)).records

    const featuredClasses = records.map(async (record) => {
      const classId = record.Class_Event_and_Outreach__r.Id
      const associatedTeachers =
        await getAssociatedTeachersForClassByIdServer(classId)

      return {
        Banner_Image__c: record.Class_Event_and_Outreach__r.Banner_Image__c,
        Genre__c: record.Class_Event_and_Outreach__r.Genre__c,
        Title__c: record.Class_Event_and_Outreach__r.Title__c,
        Duration__c: record.Class_Event_and_Outreach__r.Duration__c,
        Total_Meetings_Count__c:
          record.Class_Event_and_Outreach__r.Total_Meetings_Count__c,
        Available_Seats__c:
          record.Class_Event_and_Outreach__r.Available_Seats__c,
        Booked_Seats__c: record.Class_Event_and_Outreach__r.Booked_Seats__c,
        Id: record.Class_Event_and_Outreach__r.Id,
        associated_teachers: associatedTeachers,
      }
    })

    const returnData = await Promise.all(featuredClasses)

    return responseGenerator(true, returnData)
  } catch (error) {
    console.log(error)
    return responseGenerator(false, {
      errors: ['Error getting featured classes'],
    })
  }
}

export const getFeaturedClasses = async () => {
  try {
    const conn = await connectToSalesforce()
    const soql = `SELECT Id, Square_Cover_Image__c from Class_Event_Club__c where Featured__c = true`

    const records = (await conn.query(soql)).records

    const featuredClasses = records.map((record) => {
      return {
        class_id: record.Id,
        image: record.Square_Cover_Image__c,
      }
    })

    return responseGenerator(true, featuredClasses)
  } catch (error) {
    return responseGenerator(false, {
      error: ['Error getting featuredd classes'],
    })
  }
}

export const isRegisteredForClass = async (
  accessToken: string,
  classId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const accessTokenUserId = await getAccountId(accessToken)
    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const isRegisteredForClass = await isRegisteredForClassServer(
      accessTokenUserId,
      classId,
    )
    return responseGenerator(true, isRegisteredForClass)
  } catch (error) {
    return responseGenerator(false, {
      error: ['Error getting registration data'],
    })
  }
}

export const isApprovalRequestedForClass = async (
  accessToken: string,
  classId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const accessTokenUserId = await getAccountId(accessToken)
    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const isClassUnderApprovalForClass = await isClassUnderApprovalServer(
      accessTokenUserId,
      classId,
    )
    return responseGenerator(true, isClassUnderApprovalForClass)
  } catch (error) {
    return responseGenerator(false, {
      error: ['Error getting registration data'],
    })
  }
}

export const getSubcategoryDropdownValues = async () => {
  try {
    const picklistValues = await getPicklistOptionsServer(
      'Class_Event_Club__c',
      'Sub_category__c',
    )

    return responseGenerator(true, picklistValues)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}
