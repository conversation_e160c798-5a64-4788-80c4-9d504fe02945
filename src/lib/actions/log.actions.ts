'use server'

import { connectToSalesforce } from '@/lib/salesforce'

export const logAuthorizeNetRequestToSFServer = async (
  relatedObjectId: string,
  relatedObjectType: string,
  response: any,
) => {
  const conn = await connectToSalesforce()
  const SFObject = {
    Related_Object_Id__c: relatedObjectId,
    Related_Object_Name__c: relatedObjectType,
    Response__c: JSON.stringify(response),
    Integration_Name__c: 'Authorize.Net',
  }

  const log = await conn.sobject('Integration_Log__c').create(SFObject)

  return log.id!
}

export const updateLogAuthorizeNetResponseToSFServer = async (
  logId: string,
  response: any,
) => {
  const conn = await connectToSalesforce()
  await conn.sobject('Integration_Log__c').update({
    Id: logId,
    Response__c: JSON.stringify(response),
  })
}
