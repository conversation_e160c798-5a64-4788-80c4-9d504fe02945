'use server'

import { connectToSalesforce } from '@/lib/salesforce'
import { responseGenerator } from '@/utils/utils'

import { getAccountId } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { logAuthorizeNetRequestToSFServer, updateLogAuthorizeNetResponseToSFServer } from '@/lib/actions/log.actions'
import { AuthoriseDotNetAxios } from '@/utils/axios'
import {
  CreateCustomerProfileResponse,
  CreateCustomerPaymentProfileResponse,
  GetCustomerProfileResponse,
  DeleteCustomerPaymentProfileResponse,
} from '@/types/paymentGateway'

export const addPaymentMethod = async (
  accessToken: string,
  ccNumber: string,
  ccExpiry: string,
  ccCode: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const userId = await getAccountId(accessToken)

    if (!userId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const soql = `Select Id,Customer_Profile_ID__c,PersonEmail, FirstName, LastName from Account where Id = '${userId}'`

    const user = (await conn.query(soql)).records[0]
    let customerProfileId = user.Customer_Profile_ID__c as string
    const firstName = user.FirstName as string
    const lastName = user.LastName as string
    const email = user.PersonEmail as string

    if (!customerProfileId) {
      const [customerProfileId, customerPaymentProfileId] =
        await createCustomerProfileServer(
          userId,
          firstName,
          lastName,
          ccNumber,
          ccExpiry,
          ccCode,
          email,
        )
      await conn.sobject('Account').update({
        Id: userId,
        Customer_Profile_ID__c: customerProfileId,
      })

      return responseGenerator(true, { customerPaymentProfileId })
    }

    const customerPaymentProfileId = await createCustomerPaymentProfileServer(
      userId,
      customerProfileId,
      ccNumber,
      ccExpiry,
      ccCode,
    )

    return responseGenerator(true, { customerPaymentProfileId })
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const deletePaymentMethod = async (
  accessToken: string,
  customerPaymentProfileId: string,
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const userId = await getAccountId(accessToken)

    if (!userId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const soql = `Select Id,Customer_Profile_ID__c from Account where Id = '${userId}'`

    const user = (await conn.query(soql)).records[0]
    let customerProfileId = (user.Customer_Profile_ID__c as string) || null

    if (!customerProfileId) {
      return responseGenerator(false, { errors: ['No payment method found'] })
    }

    await deleteCustomerPaymentProfileServer(
      userId,
      customerProfileId,
      customerPaymentProfileId,
    )

    return responseGenerator(true)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getAllPaymentMethods = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const userId = await getAccountId(accessToken)

    if (!userId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const soql = `Select Id,Customer_Profile_ID__c from Account where Id = '${userId}'`

    const user = (await conn.query(soql)).records[0]
    let customerProfileId = (user.Customer_Profile_ID__c as string) || null

    if (!customerProfileId) {
      return responseGenerator(false, { errors: ['No payment methods found'] })
    }

    const paymentProfiles = await getCustomerPaymentProfilesServer(
      userId,
      customerProfileId,
    )
    return responseGenerator(true, { paymentProfiles })
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

// SERVER HELLPER FUNCTIONS
export const getOrCreateCustomerProfileServer = async (
  userId: string,
  ccNumber: string,
  ccExpiry: string,
  ccCode: string,
) => {
  const conn = await connectToSalesforce()
  const soql = `Select Id,Customer_Profile_ID__c,PersonEmail,FirstName, LastName from Account where Id = '${userId}'`

  const user = (await conn.query(soql)).records[0]
  let customerProfileIdUser = (user.Customer_Profile_ID__c as string) || null
  let profileExists = true
  const firstName = user.FirstName as string
  const lastName = user.LastName as string
  const email = user.PersonEmail as string

  if (!customerProfileIdUser) {
    const [customerProfileId, customerPaymentProfileId] =
      await createCustomerProfileServer(
        userId,
        firstName,
        lastName,
        ccNumber,
        ccExpiry,
        ccCode,
        email,
      )
    await conn.sobject('Account').update({
      Id: userId,
      Customer_Profile_ID__c: customerProfileId,
    })
    customerProfileIdUser = customerProfileId
    profileExists = false
  }
  return { customerProfileIdUser, profileExists }
}

export const createCustomerProfileServer = async (
  userId: string,
  firstName: string,
  lastName: string,
  ccNumber: string,
  ccExpiry: string,
  ccCode: string,
  email?: string,
) => {
  const requestObj = {
    createCustomerProfileRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      profile: {
        merchantCustomerId: userId,
        email: email ?? undefined,
        paymentProfiles: {
          customerType: 'individual',
          billTo: {
            firstName,
            lastName,
          },
          payment: {
            creditCard: {
              cardNumber: ccNumber,
              expirationDate: ccExpiry,
              cardCode: ccCode,
            },
          },
        },
      },
    },
  }

  const logId = await logAuthorizeNetRequestToSFServer(
    userId,
    'Account',
    requestObj,
  )

  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as CreateCustomerProfileResponse

  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)

  if (gatewayResponse.messages.resultCode === 'Error') {
    throw new Error('Error creating customer profile')
  }
  return [
    gatewayResponse.customerProfileId,
    gatewayResponse.customerPaymentProfileIdList[0],
  ]
}

export const createCustomerPaymentProfileServer = async (
  userId: string,
  customerProfileId: string,
  ccNumber: string,
  ccExpiry: string,
  ccCode: string,
) => {
  const requestObj = {
    createCustomerPaymentProfileRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      customerProfileId,
      paymentProfile: {
        payment: {
          creditCard: {
            cardNumber: ccNumber,
            expirationDate: ccExpiry,
            cardCode: ccCode,
          },
        },
        defaultPaymentProfile: false,
      },
      validationMode: 'testMode',
    },
  }

  const logId = await logAuthorizeNetRequestToSFServer(userId, 'Account', requestObj)

  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as CreateCustomerPaymentProfileResponse

  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)

  if (gatewayResponse.messages.resultCode === 'Error') {
    const errorMessage = gatewayResponse.messages.message[0].text
    throw new Error(errorMessage)
  }

  return gatewayResponse.customerPaymentProfileId
}

export const deleteCustomerPaymentProfileServer = async (
  userId: string,
  customerProfileId: string,
  customerPaymentProfileId: string,
) => {
  const requestObj = {
    deleteCustomerPaymentProfileRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      customerProfileId,
      customerPaymentProfileId,
    },
  }

  const logId = await logAuthorizeNetRequestToSFServer(userId, 'Account', requestObj)

  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as DeleteCustomerPaymentProfileResponse

  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)
  if (gatewayResponse.messages.resultCode === 'Error') {
    const errorMessage = gatewayResponse.messages.message[0].text
    throw new Error(errorMessage)
  }

  return gatewayResponse
}

export const getCustomerPaymentProfilesServer = async (
  userId: string,
  customerProfileId: string,
) => {
  const requestObj = {
    getCustomerProfileRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      customerProfileId,
    },
  }

  const logId = await logAuthorizeNetRequestToSFServer(userId, 'Account', requestObj)
  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as GetCustomerProfileResponse
  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)
  if (gatewayResponse.messages.resultCode === 'Error') {
    const errorMessage = gatewayResponse.messages.message[0].text
    throw new Error(errorMessage)
  }

  return gatewayResponse.profile
}
