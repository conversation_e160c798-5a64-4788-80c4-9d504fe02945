'use server'

import { connectToSalesforce } from '@/lib/salesforce'

//RECORD TYPE
export const getRecordTypeByName = async (
  recordTypeName:
    | 'Organization'
    | 'PersonAccount'
    | 'Adult_Class'
    | 'Event'
    | 'Outreach'
    | 'Youth_Class'
    | 'Teaching_Application'
    | 'Tuition_Assistance_Application'
    | 'Teen_Writers_Fellowship_Application',
  sobjectName: 'Account' | 'Class_Event_Club__c' | 'Application__c',
) => {
  const conn = await connectToSalesforce()
  const recordType = await conn.sobject('RecordType').findOne({
    DeveloperName: recordTypeName,
    IsActive: true,
    SobjectType: sobjectName,
  })

  if (!recordType?.Id) {
    throw new Error(`Record type not found`)
  }

  return recordType.Id
}

export const getDonationProduct = async (donationType: string) => {
  const conn = await connectToSalesforce()
  const product = await conn.query(
    `Select Id,Name,Family from Product2 where Family='Donation' AND Name='${donationType}'`,
  )

  if (!product.records[0]) {
    throw new Error(`Donation product not found`)
  }

  return product.records[0]
}
