'use server'
import { cookies } from 'next/headers'
import { StackAuthServerAxios } from '@/utils/axios'
import { User } from '@/interfaces/user'
import { getPublicUrl } from '../aws'

export const refreshCustomAccountSession = async (refreshToken: string) => {
  const response = await StackAuthServerAxios.post(
    '/auth/sessions/current/refresh',
    {},
    {
      headers: {
        'x-stack-refresh-token': refreshToken,
      },
    },
  )
  return response.data
}

export const verifyCustomAccountSession = async (accessToken: string) => {
  try {
    const response = await StackAuthServerAxios.get('/users/me', {
      headers: {
        'x-stack-access-token': accessToken,
      },
    })
    const user = response.data

    if (user.id) {
      return true
    } else {
      return false
    }
  } catch (error) {
    return false
  }
}

export async function logout() {
  const cookieStore = cookies()
  const allCookies = cookieStore.getAll()
  for (const cookie of allCookies) {
    cookieStore.set(cookie.name, '', { maxAge: 0, path: '/' })
  }
}

export async function getSessionKey(key: string) {
  const value = cookies().get(key)?.value
  if (!value) return null
  return value
}
export async function handleLoggedIn(): Promise<User | null> {
  const user = await getSessionKey('user')
  if (user) {
    return JSON.parse(user) as User
  }
  return null
}

export async function handleAuthResponse(responseData: any) {
  if (!responseData) return null
  const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
  const authToken = responseData?.access_token
  const refreshToken = responseData?.refresh_token
  let userRoles = responseData?.Roles__r?.records || []
  userRoles = userRoles.map((role: any) => {
    if (role?.Is_Active__c) {
      return role?.Role_Type__c
    }
  })

  const user = {
    id: responseData?.Id,
    name: responseData?.Name,
    email: responseData?.PersonEmail,
    roles: userRoles,
    role: responseData?.Last_Used_Role__c ?? 'Youth Student',
    photo:
      responseData?.Photo__c &&
      (responseData?.Photo__c as string).startsWith('http')
        ? responseData?.Photo__c
        : await getPublicUrl(responseData?.Photo__c),
    location: responseData?.BillingCity,
    birthday: responseData?.PersonBirthdate,
    bio: responseData?.Bio__c,
    phoneNumber: responseData?.Phone,
    totalClass: responseData?.totalClasses || 0,
    instagram: responseData?.Instagram__c,
    facebook: responseData?.Facebook__c,
    twitter: responseData?.Twitter__c,
    website: responseData?.Website,
    youtube:responseData?.YouTube__c,
    substack:responseData?.Substack__c,
    favoriteGenres: responseData?.Favorite_Genres__c,
    favoriteAuthors: <AUTHORS>
    BillingAddress: responseData?.BillingAddress,
    ShippingAddress: responseData?.ShippingAddress,
    emailNotificationsAllowed: responseData?.Email_Notifications_Allowed__c,
    parent: responseData?.Parent_Account__c,
  }

  if (authToken && refreshToken) {
    const stackAccessValue = [refreshToken, authToken]
    cookies().set('stack-access', JSON.stringify(stackAccessValue), {
      expires,
      httpOnly: true,
    })
  }

  if (user) {
    cookies().set('user', JSON.stringify(user), { expires, httpOnly: true })
  }
  return user
}

export async function switchRole(role: string) {
  const user = await handleLoggedIn()
  if (!user) return null

  if (role) {
    const updatedUser = { ...user, role }
    cookies().set('user', JSON.stringify(updatedUser), {
      expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      httpOnly: true,
    })
    return updatedUser
  }
  return null
}

export const handleToken = async () => {
  const stackAccess: any = await getSessionKey('stack-access')
  if (!stackAccess) {
    throw new Error('No auth token found')
  }
  const refreshToken = JSON.parse(stackAccess)[0]
  const authToken = JSON.parse(stackAccess)[1]
  const tokenNotExpired = await verifyCustomAccountSession(authToken)

  if (tokenNotExpired) {
    return authToken
  }

  if (refreshToken) {
    const newToken = await refreshCustomAccountSession(refreshToken)
    const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)

    if (newToken) {
      const stackAccessValue = [refreshToken, newToken.access_token]
      cookies().set('stack-access', JSON.stringify(stackAccessValue), {
        expires,
        httpOnly: true,
      })
      return newToken.access_token
    } else {
      await logout()
    }
  }
  return null
}

export const getToken = async (type: string = 'access') => {
  const accessToken = await getSessionKey('stack-access')
  if (!accessToken) return null
  try {
    const parsedToken = JSON.parse(accessToken)
    if (Array.isArray(parsedToken) && parsedToken.length === 2) {
      switch (type) {
        case 'access':
          return parsedToken[1]
        case 'refresh':
          return parsedToken[0]
        default:
          return parsedToken
      }
    }
    return accessToken
  } catch (e) {
    return ''
  }
}

export async function updateUser(key: string, value: any) {
  const user = await handleLoggedIn()
  if (!user) return null

  const updatedUser = { ...user, [key]: value }
  if (updatedUser) {
    // Set the user cookie with the updated user data
    cookies().set('user', JSON.stringify(updatedUser), {
      expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      httpOnly: true,
    })
  }
  return updatedUser
}

// Generic function to update multiple user fields at once
export async function updateUserFields(updates: Record<string, any>) {
  const user = await handleLoggedIn()
  if (!user) return null

  const updatedUser = { ...user, ...updates }
  if (updatedUser) {
    // Set the user cookie with the updated user data
    cookies().set('user', JSON.stringify(updatedUser), {
      expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      httpOnly: true,
    })
  }
  return updatedUser
}

// Generic function to update nested object fields (like BillingAddress, ShippingAddress)
export async function updateUserNestedField(
  parentKey: string,
  updates: Record<string, any>,
) {
  const user = await handleLoggedIn()
  if (!user) return null

  const currentParentValue = user[parentKey as keyof typeof user] || {}

  // Handle the case where currentParentValue might be null or undefined
  const safeCurrentValue =
    currentParentValue && typeof currentParentValue === 'object'
      ? currentParentValue
      : {}

  const updatedParentValue = {
    ...safeCurrentValue,
    ...updates,
  }

  const updatedUser = { ...user, [parentKey]: updatedParentValue }
  if (updatedUser) {
    // Set the user cookie with the updated user data
    cookies().set('user', JSON.stringify(updatedUser), {
      expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      httpOnly: true,
    })
  }
  return updatedUser
}
