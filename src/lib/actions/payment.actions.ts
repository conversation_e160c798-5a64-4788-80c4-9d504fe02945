'use server'
import { differenceInMinutes, format, differenceInDays } from 'date-fns'
import jsforce from 'jsforce'

import { connectToSalesforce } from '@/lib/salesforce'
import { responseGenerator } from '@/utils/utils'
import { AuthoriseDotNetAxios } from '@/utils/axios'
import { verifyPaymentToken } from '@/lib/jwt'
import { hashString, calculateProcessingFee } from '@/utils/utils'
import {
  ChargeCreditCardResponse,
  ChargeCustomerProfileResponse,
  ChargeCreditCardRecurringResponse,
} from '@/types/paymentGateway'

import { IPaymentToken, IPaymentTokenSFDocument, Schedule } from '@/models/paymentToken.model'
import { getWalletBalanceServer } from '@/lib/actions/wallet.actions'
import {
  getOrCreateCustomerProfileServer,
  createCustomerPaymentProfileServer,
} from '@/lib/actions/customerProfile.actions'
import { createCouponUsageServer } from '@/lib/actions/coupon.actions'
import { getClassProductServer } from '@/lib/actions/class.actions'
import { logAuthorizeNetRequestToSFServer, updateLogAuthorizeNetResponseToSFServer } from '@/lib/actions/log.actions'
import { PaymentType } from '@/utils/constants'

const GatewayStatusEnum = {
  '1': 'Approved',
  '2': 'Declined',
  '3': 'Error',
  '4': 'Held for Review',
}

const USER_WALLET = 'UserWallet'

type CreditCardInfo = {
  ccNumber: string
  ccExpiry: string
  ccCode: string
  ccName: string
  saveCard: boolean
}

type SavedProfileInfo = {
  customerProfileId: string
  paymentProfileId: string
}

type PaymentInfo = CreditCardInfo | SavedProfileInfo


export const getActivePaymentToken = async (token: string): Promise<IPaymentTokenSFDocument | null> => {
  const conn = await connectToSalesforce();
  const tokenHash = hashString(token);
  const query = {
    Token_Hash__c: tokenHash,
    Status__c: 'active',
  }

  const paymentToken = (await conn.sobject('Payment_Token__c').findOne(query)) as IPaymentTokenSFDocument
  return paymentToken
}

export const createPaymentTokenSF = async (
  tokenData: IPaymentToken,
) => { 
  const conn = await connectToSalesforce();
  const tokenHash = hashString(tokenData.token);
  const paymentToken = {
    User_Id__c: tokenData.userId,
    Token__c: tokenData.token,
    Token_Hash__c: tokenHash,
    Status__c: tokenData.status,
    Payment_Type__c: tokenData.paymentType,
    Occurences__c: tokenData.occurrences,
    Schedule__c: tokenData.schedule,
    Amount__c: tokenData.amount,
  }
  await conn.sobject('Payment_Token__c').create(paymentToken)
}

export const markPaymentTokenAsCompleted = async(tokenSFData: IPaymentTokenSFDocument) => {
  const conn = await connectToSalesforce();
  await conn.sobject('Payment_Token__c').update({
    Id: tokenSFData.Id,
    Status__c: 'completed',
  })
}

export const markPaymentTokenAsFailed = async(tokenSFData: IPaymentTokenSFDocument) => {
  const conn = await connectToSalesforce();
  await conn.sobject('Payment_Token__c').update({
    Id: tokenSFData.Id,
    Status__c: 'failed',
  })
}

export const chargeCustomerProfileByPaymentType = async (
  paymentType: PaymentType,
  customerProfileId: string,
  paymentProfileId: string,
  orderId: string,
  amount: number,
  schedule?: Schedule,
  occurrences?: string,
) => {
  if (paymentType === 'One-time') {
    const chargeCustomerProfileResponse = await chargeCustomerProfileRequest(
      amount,
      customerProfileId,
      paymentProfileId,
      orderId,
    )
  } else if (paymentType === 'Subscription' && schedule && occurrences) {
    const customerProfileChargeResponse =
      await chargeCustomerProfileRecurringRequest(
        amount,
        customerProfileId,
        paymentProfileId,
        schedule,
        occurrences,
        orderId,
      )
  } else {
    throw new Error('Invalid payment details')
  }
}

export const chargeCreditCardByPaymentType = async (
  paymentType: PaymentType,
  ccNumber: string,
  ccExpiry: string,
  ccCode: string,
  orderId: string,
  amount: number,
  userId: string,
  schedule?: Schedule,
  occurrences?: string,
) => {
  if (paymentType === 'One-time') {
    const chargeCreditCardResponse = await createChargeCreditCardRequest(
      amount,
      ccNumber,
      ccExpiry,
      ccCode,
      orderId,
    )
  } else if (paymentType === 'Subscription' && schedule && occurrences) {
    const conn = await connectToSalesforce()
    const soql = `Select FirstName, LastName FROM Account WHERE Id = '${userId}'`
    const records = (await conn.query(soql)).records
    const user = records[0]

    const firstName = user.FirstName as string
    const lastName = user.LastName as string

    const creditCardChargeResponse =
      await createChargeCreditCardRecurringRequest(
        amount,
        ccNumber,
        ccExpiry,
        ccCode,
        schedule,
        occurrences,
        firstName,
        lastName,
        orderId,
      )
  } else {
    throw new Error('Invalid payment details')
  }
}

export const getPriceBookIdByProductServer = async (productId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id FROM PricebookEntry  WHERE Product2Id = '${productId}'`
  const records = (await conn.query(soql)).records

  return records[0].Id!
}

export const chargeCustomerProfileRequest = async (
  amount: number,
  customerProfileId: string,
  paymentProfileId: string,
  orderId: string,
) => {
  const requestObj = {
    createTransactionRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      refId: orderId,
      transactionRequest: {
        transactionType: 'authCaptureTransaction',
        amount: amount,
        profile: {
          customerProfileId: customerProfileId,
          paymentProfile: { paymentProfileId: paymentProfileId },
        },
      },
    },
  }
  const logId = await logAuthorizeNetRequestToSFServer(orderId, 'Order', requestObj)
  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as ChargeCustomerProfileResponse
  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)

  if (gatewayResponse.transactionResponse.responseCode !== '1') {
    const errorMessage = gatewayResponse.transactionResponse.errors[0].errorText
    throw new Error(errorMessage)
  }

  return gatewayResponse
}

export const chargeCustomerProfileRecurringRequest = async (
  amount: number,
  customerProfileId: string,
  paymentProfileId: string,
  type: 'Monthly' | 'Annual',
  occurrences: string,
  orderId: string,
) => {
  const today = format(new Date(), 'yyyy-MM-dd')
  const requestObj = {
    ARBCreateSubscriptionRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      refId: orderId,
      subscription: {
        name: orderId,
        paymentSchedule: {
          interval: {
            length: type === 'Monthly' ? '1' : '12',
            unit: 'months',
          },
          startDate: today,
          totalOccurrences: occurrences,
        },
        amount: amount,
        order: {
          invoiceNumber: orderId,
        },
        profile: {
          customerProfileId: customerProfileId,
          customerPaymentProfileId: paymentProfileId,
        },
      },
    },
  }
  const logId = await logAuthorizeNetRequestToSFServer(orderId, 'Order', requestObj)
  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as ChargeCreditCardRecurringResponse
  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)

  if (gatewayResponse.messages.message[0].code.startsWith('E')) {
    const errorMessage = gatewayResponse.messages.message[0].text
    throw new Error(errorMessage)
  }

  return gatewayResponse
}

export const createChargeCreditCardRecurringRequest = async (
  amount: number,
  ccNumber: string,
  ccExpiry: string,
  ccCode: string,
  type: 'Monthly' | 'Annual',
  occurrences: string,
  firstName: string,
  lastName: string,
  orderId: string,
) => {
  const today = format(new Date(), 'yyyy-MM-dd')
  const requestObj = {
    ARBCreateSubscriptionRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      refId: orderId,
      subscription: {
        name: orderId,
        paymentSchedule: {
          interval: {
            length: type === 'Monthly' ? '1' : '12',
            unit: 'months',
          },
          startDate: today,
          totalOccurrences: occurrences,
        },
        amount: amount,
        payment: {
          creditCard: {
            cardNumber: ccNumber,
            expirationDate: ccExpiry,
            cardCode: ccCode,
          },
        },
        order: {
          invoiceNumber: orderId,
        },
        billTo: {
          firstName,
          lastName,
        },
      },
    },
  }
  const logId = await logAuthorizeNetRequestToSFServer(orderId, 'Order', requestObj)
  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as ChargeCreditCardRecurringResponse
  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)
  if (gatewayResponse.messages.message[0].code.startsWith('E')) {
    const errorMessage = gatewayResponse.messages.message[0].text
    throw new Error(errorMessage)
  }
  return gatewayResponse
}

export const createCancelRecurringRequest = async (
  subscriptionId: string,
  orderId: string,
) => {
  const requestObj = {
    ARBCancelSubscriptionRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      refId: orderId,
      subscriptionId: subscriptionId,
    },
  }
  const logId = await logAuthorizeNetRequestToSFServer(orderId, 'Order', requestObj)
  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as ChargeCreditCardRecurringResponse
  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)
  return gatewayResponse
}

export const createChargeCreditCardRequest = async (
  amount: number,
  ccNumber: string,
  ccExpiry: string,
  ccCode: string,
  orderId: string,
) => {
  const requestObj = {
    createTransactionRequest: {
      merchantAuthentication: {
        name: process.env.AUTHORIZE_NET_API_LOGIN_ID,
        transactionKey: process.env.AUTHORIZE_NET_TRANSACTION_KEY,
      },
      refId: orderId,
      transactionRequest: {
        transactionType: 'authCaptureTransaction',
        amount: amount,
        payment: {
          creditCard: {
            cardNumber: ccNumber,
            expirationDate: ccExpiry,
            cardCode: ccCode,
          },
        },
      },
    },
  }
  const logId = await logAuthorizeNetRequestToSFServer(orderId, 'Order', requestObj)
  const response = await AuthoriseDotNetAxios.post('/', requestObj)

  const gatewayResponse = response.data as ChargeCreditCardResponse
  await updateLogAuthorizeNetResponseToSFServer(logId, gatewayResponse)

  if (gatewayResponse.transactionResponse.responseCode !== '1') {
    const errorMessage = gatewayResponse.transactionResponse.errors[0].errorText
    throw new Error(errorMessage)
  }

  return gatewayResponse
}

export const customCheckoutDonationFlow = async (
  userId: string,
  entityId: string,
  amount: number,
  addressLine1: string,
  addressLine2: string,
  city: string,
  state: string,
  country: string,
  zip: string,
  shippingAddressLine1: string,
  shippingAddressLine2: string,
  shippingCity: string,
  shippingState: string,
  shippingCountry: string,
  shippingZip: string,
  token: string,
  useWalletBalance: boolean,
  amountToDebitFromWallet: number,
  isCardPayment: boolean,
  isDonationAnonymous?: boolean,
  paymentInfo?: PaymentInfo,
) => {
  const conn = await connectToSalesforce()
 const paymentTokenData = await getActivePaymentToken(token)

  if (!paymentTokenData) {
    return responseGenerator(false, { errors: ['Invalid payment token'] })
  }

  let customerProfileId, paymentProfileId, ccNumber, ccExpiry, ccCode

  const userWalletBalance = await getWalletBalanceServer(userId)
  const effectiveAmount = useWalletBalance
    ? Math.max(0, amount - userWalletBalance)
    : amount

  const shouldChargeUser = effectiveAmount > 0

  if (shouldChargeUser) {
    if (isCardPayment) {
      const creditCardInfo = paymentInfo as CreditCardInfo
      ccNumber = creditCardInfo.ccNumber
      ccExpiry = creditCardInfo.ccExpiry
      ccCode = creditCardInfo.ccCode
    } else {
      const savedProfileInfo = paymentInfo as SavedProfileInfo
      if (savedProfileInfo) {
        customerProfileId = savedProfileInfo.customerProfileId
        paymentProfileId = savedProfileInfo.paymentProfileId
      }
    }
  }

  try {
    //create order object in salesforce
    const donationOrder = await conn.sobject('Order').create({
      Account__c: userId,
      AccountId: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
      Status: 'Draft',
      EffectiveDate: jsforce.SfDate.toDateLiteral(new Date()),
    })

    //update account with latest billing and shipping address
    await conn.sobject('Account').update({
      Id: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
    })
    const orderId = donationOrder.id

    if (!orderId) {
      throw new Error('Failed to create order')
    }

    const pricebookId = await getPriceBookIdByProductServer(entityId)
    //create order item object in salesforce
    const orderItem = {
      Product2Id: entityId,
      Account__c: userId,
      Quantity: 1,
      OrderId: orderId,
      UnitPrice: amount,
      Anonymous_Donation__c: !!isDonationAnonymous,
      PricebookEntryId: pricebookId,
    }
    await conn.sobject('OrderItem').create(orderItem)

    //charge the credit card if the effective amount is greater than 0
    if (shouldChargeUser) {
      if (customerProfileId && paymentProfileId) {
        const chargeCustomerProfileResponse =
          await chargeCustomerProfileRequest(
            effectiveAmount,
            customerProfileId,
            paymentProfileId,
            orderId,
          )
      } else if (ccNumber && ccExpiry && ccCode) {
        const chargeCreditCardResponse = await createChargeCreditCardRequest(
          effectiveAmount,
          ccNumber,
          ccExpiry,
          ccCode,
          orderId,
        )
      } else {
        throw new Error('Invalid payment details')
      }
    }

    //create a transaction for wallet if the user has opted to use wallet balance
    if (useWalletBalance && amountToDebitFromWallet > 0) {
      const walletTransaction = {
        Account__c: userId,
        Amount__c: -amountToDebitFromWallet,
      }
      const walletTransactionObj = await conn
        .sobject('Transaction__c')
        .create(walletTransaction)

      //create a relavant payment object in salesforce for wallet
      const paymentObject = {
        Account__c: userId,
        Account_Number__c: userId,
        Account_Type__c: USER_WALLET,
        Amount__c: amountToDebitFromWallet,
        Order__c: orderId,
        Status__c: GatewayStatusEnum['1'],
        Transaction_Hash__c: hashString(
          walletTransactionObj.id + userId + orderId + amount,
        ),
        Transaction_ID__c: walletTransactionObj.id,
      }
      const paymentSFObject = await conn
        .sobject('Payment__c')
        .create(paymentObject)

      //associate wallet transaction with payment object
      await conn.sobject('Transaction__c').update({
        Id: walletTransactionObj.id!,
        Payment__c: paymentSFObject.id!,
      })
    }

    //update the payment token status to completed
    await markPaymentTokenAsCompleted(paymentTokenData)
    return responseGenerator(true, null)
  } catch (error: any) {
    await markPaymentTokenAsFailed(paymentTokenData)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const customCheckoutInvoiceFlow = async (
  userId: string,
  entityId: string,
  amount: number,
  addressLine1: string,
  addressLine2: string,
  city: string,
  state: string,
  country: string,
  zip: string,
  shippingAddressLine1: string,
  shippingAddressLine2: string,
  shippingCity: string,
  shippingState: string,
  shippingCountry: string,
  shippingZip: string,
  token: string,
  useWalletBalance: boolean,
  amountToDebitFromWallet: number,
  isCardPayment: boolean,
  isDonationAnonymous?: boolean,
  paymentInfo?: PaymentInfo,
) => {
  const conn = await connectToSalesforce()
  const paymentTokenData = await getActivePaymentToken(token)

  if (!paymentTokenData) {
    return responseGenerator(false, { errors: ['Invalid payment token'] })
  }

  let customerProfileId, paymentProfileId, ccNumber, ccExpiry, ccCode

  const userWalletBalance = await getWalletBalanceServer(userId)
  const effectiveAmount = useWalletBalance
    ? Math.max(0, amount - userWalletBalance)
    : amount
  const shouldChargeUser = effectiveAmount > 0

  if (shouldChargeUser) {
    if (isCardPayment) {
      const creditCardInfo = paymentInfo as CreditCardInfo
      ccNumber = creditCardInfo.ccNumber
      ccExpiry = creditCardInfo.ccExpiry
      ccCode = creditCardInfo.ccCode
    } else {
      const savedProfileInfo = paymentInfo as SavedProfileInfo
      customerProfileId = savedProfileInfo.customerProfileId
      paymentProfileId = savedProfileInfo.paymentProfileId
    }
  }

  try {
    //create order object in salesforce
    const invoiceOrder = await conn.sobject('Order').create({
      Account__c: userId,
      AccountId: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
      Status: 'Draft',
      EffectiveDate: jsforce.SfDate.toDateLiteral(new Date()),
    })

    //update account with latest billing and shipping address
    await conn.sobject('Account').update({
      Id: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
    })
    const orderId = invoiceOrder.id

    if (!orderId) {
      throw new Error('Failed to create order')
    }

    //for every invoice line item, create an orderItem object in salesforce
    const soql = `SELECT Class_Event_and_Outreach__c, Item_Total__c FROM Invoice_Line_Item__c WHERE Invoice__c = '${entityId}'`
    const invoiceLineItems = (await conn.query(soql)).records

    for (const invoiceLineItem of invoiceLineItems) {
      const classId = invoiceLineItem.Class_Event_and_Outreach__c
      const classProductId = await getClassProductServer(classId)
      const pricebookId = await getPriceBookIdByProductServer(classProductId)
      const orderItem = {
        Product2Id: classProductId,
        Account__c: userId,
        Quantity: 1,
        OrderId: orderId,
        UnitPrice: invoiceLineItem.Item_Total__c,
        PricebookEntryId: pricebookId,
      }

      await conn.sobject('OrderItem').create(orderItem)
    }

    //charge the credit card if the effective amount is greater than 0
    if (shouldChargeUser) {
      if (customerProfileId && paymentProfileId) {
        const chargeCustomerProfileResponse =
          await chargeCustomerProfileRequest(
            effectiveAmount,
            customerProfileId,
            paymentProfileId,
            orderId,
          )
      } else if (ccNumber && ccExpiry && ccCode) {
        const chargeCreditCardResponse = await createChargeCreditCardRequest(
          effectiveAmount,
          ccNumber,
          ccExpiry,
          ccCode,
          orderId,
        )
      } else {
        throw new Error('Invalid payment details')
      }
    }

    //not using wallet balance here

    //update the payment token status to completed
    await markPaymentTokenAsCompleted(paymentTokenData)

    return responseGenerator(true, null)
  } catch (error) {
    await markPaymentTokenAsFailed(paymentTokenData)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const customCheckoutSubscriptionFlow = async (
  userId: string,
  entityId: string,
  amount: number,
  addressLine1: string,
  addressLine2: string,
  city: string,
  state: string,
  country: string,
  zip: string,
  shippingAddressLine1: string,
  shippingAddressLine2: string,
  shippingCity: string,
  shippingState: string,
  shippingCountry: string,
  shippingZip: string,
  token: string,
  useWalletBalance: boolean,
  amountToDebitFromWallet: number,
  isCardPayment: boolean,
  isDonationAnonymous?: boolean,
  paymentInfo?: PaymentInfo,
) => {
  const conn = await connectToSalesforce()
 const paymentTokenData = await getActivePaymentToken(token)

  if (!paymentTokenData) {
    return responseGenerator(false, { errors: ['Invalid payment token'] })
  }

  const schedule = paymentTokenData.Schedule__c!
  const occurrences = paymentTokenData.Occurences__c.toString()

  let customerProfileId, paymentProfileId, ccNumber, ccExpiry, ccCode

  const userWalletBalance = await getWalletBalanceServer(userId)
  const effectiveAmount = useWalletBalance
    ? Math.max(0, amount - userWalletBalance)
    : amount
  const shouldChargeUser = effectiveAmount > 0

  if (shouldChargeUser) {
    if (isCardPayment) {
      const creditCardInfo = paymentInfo as CreditCardInfo
      ccNumber = creditCardInfo.ccNumber
      ccExpiry = creditCardInfo.ccExpiry
      ccCode = creditCardInfo.ccCode
    } else {
      const savedProfileInfo = paymentInfo as SavedProfileInfo
      customerProfileId = savedProfileInfo.customerProfileId
      paymentProfileId = savedProfileInfo.paymentProfileId
    }
  }

  try {
    //create order object in salesforce
    const subscriptionOrder = await conn.sobject('Order').create({
      Account__c: userId,
      AccountId: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
      Status: 'Draft',
      EffectiveDate: jsforce.SfDate.toDateLiteral(new Date()),
    })

    //update account with latest billing and shipping address
    await conn.sobject('Account').update({
      Id: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
    })
    const orderId = subscriptionOrder.id

    if (!orderId) {
      throw new Error('Failed to create order')
    }

    //create orderItem
    const pricebookId = await getPriceBookIdByProductServer(entityId)
    const orderItem = {
      Product2Id: entityId,
      Account__c: userId,
      Quantity: 1,
      OrderId: orderId,
      UnitPrice: amount,
      PricebookEntryId: pricebookId,
      Subscription_Term__c: schedule,
    }

    const subscriptionOrderItem = await conn
      .sobject('OrderItem')
      .create(orderItem)

    const orderItemId = subscriptionOrderItem.id
    if (!orderItemId) {
      throw new Error('Failed to create order')
    }

    //charge the credit card if the effective amount is greater than 0
    if (shouldChargeUser) {
      if (customerProfileId && paymentProfileId) {
        //create customerProfileId subscription
        const customerProfileChargeResponse =
          await chargeCustomerProfileRecurringRequest(
            effectiveAmount,
            customerProfileId,
            paymentProfileId,
            schedule,
            occurrences,
            orderId,
          )
      } else if (ccNumber && ccExpiry && ccCode) {
        //create credit card subscription
        const soql = `Select FirstName, LastName FROM Account WHERE Id = '${userId}'`
        const records = (await conn.query(soql)).records
        const user = records[0]

        const firstName = user.FirstName as string
        const lastName = user.LastName as string

        const creditCardChargeResponse =
          await createChargeCreditCardRecurringRequest(
            effectiveAmount,
            ccNumber,
            ccExpiry,
            ccCode,
            schedule,
            occurrences,
            firstName,
            lastName,
            orderItemId,
          )
      } else {
        throw new Error('Invalid payment details')
      }
    }

    //not using wallet balance here

    //update the payment token status to completed
    await markPaymentTokenAsCompleted(paymentTokenData)

    return responseGenerator(true, null)
  } catch (error) {
    await markPaymentTokenAsFailed(paymentTokenData)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const customCheckoutWalletFlow = async (
  userId: string,
  amount: number,
  token: string,
  isCardPayment: boolean,
  paymentInfo?: PaymentInfo,
) => {
  const conn = await connectToSalesforce()
 const paymentTokenData = await getActivePaymentToken(token)

  if (!paymentTokenData) {
    return responseGenerator(false, { errors: ['Invalid payment token'] })
  }

  let customerProfileId, paymentProfileId, ccNumber, ccExpiry, ccCode

  const shouldChargeUser = amount > 0

  if (shouldChargeUser) {
    if (isCardPayment) {
      const creditCardInfo = paymentInfo as CreditCardInfo
      ccNumber = creditCardInfo.ccNumber
      ccExpiry = creditCardInfo.ccExpiry
      ccCode = creditCardInfo.ccCode
    } else {
      const savedProfileInfo = paymentInfo as SavedProfileInfo
      customerProfileId = savedProfileInfo.customerProfileId
      paymentProfileId = savedProfileInfo.paymentProfileId
    }
  }

  try {
    //charge credit card if the effective amount is greater than 0
    if (shouldChargeUser) {
      if (customerProfileId && paymentProfileId) {
        const chargeCustomerProfileResponse =
          await chargeCustomerProfileRequest(
            amount,
            customerProfileId,
            paymentProfileId,
            '',
          )
      } else if (ccNumber && ccExpiry && ccCode) {
        const chargeCreditCardResponse = await createChargeCreditCardRequest(
          amount,
          ccNumber,
          ccExpiry,
          ccCode,
          '',
        )
      } else {
        throw new Error('Invalid payment details')
      }
    }
    //create a transaction for wallet to credit the amount
    const walletTransaction = {
      Account__c: userId,
      Amount__c: amount,
    }

    await conn.sobject('Transaction__c').create(walletTransaction)

    //update the payment token status to completed
    await markPaymentTokenAsCompleted(paymentTokenData)

    return responseGenerator(true, null)
  } catch (error) {
    await markPaymentTokenAsFailed(paymentTokenData)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const customCheckoutCartFlow = async (
  userId: string,
  amount: number,
  addressLine1: string,
  addressLine2: string,
  city: string,
  state: string,
  country: string,
  zip: string,
  shippingAddressLine1: string,
  shippingAddressLine2: string,
  shippingCity: string,
  shippingState: string,
  shippingCountry: string,
  shippingZip: string,
  token: string,
  useWalletBalance: boolean,
  amountToDebitFromWallet: number,
  isCardPayment: boolean,
  paymentInfo?: PaymentInfo,
  couponCode?: string,
  discountedAmout?: number,
) => {
  const conn = await connectToSalesforce()
 const paymentTokenData = await getActivePaymentToken(token)

  if (!paymentTokenData) {
    return responseGenerator(false, { errors: ['Invalid payment token'] })
  }

  const schedule = paymentTokenData.Schedule__c!
  const occurrences = paymentTokenData.Occurences__c.toString()
  const paymentType = paymentTokenData.Payment_Type__c

  let customerProfileId, paymentProfileId, ccNumber, ccExpiry, ccCode

  const userWalletBalance = await getWalletBalanceServer(userId)
  const effectiveAmount = useWalletBalance
    ? Math.max(0, amount - userWalletBalance)
    : amount

  const shouldChargeUser = effectiveAmount > 0

  if (shouldChargeUser) {
    if (isCardPayment) {
      const creditCardInfo = paymentInfo as CreditCardInfo
      ccNumber = creditCardInfo.ccNumber
      ccExpiry = creditCardInfo.ccExpiry
      ccCode = creditCardInfo.ccCode
    } else {
      const savedProfileInfo = paymentInfo as SavedProfileInfo
      customerProfileId = savedProfileInfo.customerProfileId
      paymentProfileId = savedProfileInfo.paymentProfileId
    }
  }

  try {
    //create order object in salesforce
    const cartOrder = await conn.sobject('Order').create({
      Account__c: userId,
      AccountId: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
      Status: 'Draft',
      EffectiveDate: jsforce.SfDate.toDateLiteral(new Date()),
      Discount__c: discountedAmout,
    })

    //update account with latest billing and shipping address
    await conn.sobject('Account').update({
      Id: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
    })

    const orderId = cartOrder.id
    if (!orderId) {
      throw new Error('Failed to create order')
    }

    //for every product in the cartItem, create an orderItem object in salesforce
    const soql = `Select Id,Product__r.Id, Unit_Price__c, Attendance_Type__c from Cart_Item__c where Account__c ='${userId}'`
    const cartItems = (await conn.query(soql)).records

    for (const cartItem of cartItems) {
      const productId = cartItem.Product__r.Id!
      const pricebookId = await getPriceBookIdByProductServer(productId)
      const orderItem = {
        Product2Id: cartItem.Product__r.Id,
        Account__c: userId,
        Quantity: 1,
        OrderId: orderId,
        UnitPrice: cartItem.Unit_Price__c,
        Attendance_Type__c: cartItem.Attendance_Type__c,
        PricebookEntryId: pricebookId,
      }
      await conn.sobject('OrderItem').create(orderItem)
    }

    //create couponUsage records if couponCode is used
    if (couponCode && discountedAmout) {
      await createCouponUsageServer(userId, couponCode, discountedAmout)
    }

    //charge the credit card if the effective amount is greater than 0
    if (shouldChargeUser) {
      if (customerProfileId && paymentProfileId) {
        await chargeCustomerProfileByPaymentType(
          paymentType,
          customerProfileId,
          paymentProfileId,
          orderId,
          effectiveAmount,
          schedule,
          occurrences,
        )
      } else if (ccNumber && ccExpiry && ccCode) {
        await chargeCreditCardByPaymentType(
          paymentType,
          ccNumber,
          ccExpiry,
          ccCode,
          orderId,
          effectiveAmount,
          userId,
          schedule,
          occurrences,
        )
      } else {
        throw new Error('Invalid payment details')
      }
    }

    //create a transaction for wallet if the user has opted to use wallet balance
    if (useWalletBalance && amountToDebitFromWallet > 0) {
      const walletTransaction = {
        Account__c: userId,
        Amount__c: -amountToDebitFromWallet,
      }
      const walletTransactionObj = await conn
        .sobject('Transaction__c')
        .create(walletTransaction)

      //create a relavant payment object in salesforce for wallet
      const paymentObject = {
        Account__c: userId,
        Account_Number__c: userId,
        Account_Type__c: USER_WALLET,
        Amount__c: amountToDebitFromWallet,
        Order__c: orderId,
        Status__c: GatewayStatusEnum['1'],
        Transaction_Hash__c: hashString(
          walletTransactionObj.id + userId + orderId + amount,
        ),
        Transaction_ID__c: walletTransactionObj.id,
      }
      const paymentSFObject = await conn
        .sobject('Payment__c')
        .create(paymentObject)

      //associate wallet transaction with payment object
      await conn.sobject('Transaction__c').update({
        Id: walletTransactionObj.id!,
        Payment__c: paymentSFObject.id!,
      })
    }

    //empty the cart
    for (const cartItem of cartItems) {
      await conn.sobject('Cart_Item__c').delete(cartItem.Id as string)
    }

    //update the payment token status to completed
    await markPaymentTokenAsCompleted(paymentTokenData)

    return responseGenerator(true, null)
  } catch (error) {
    await markPaymentTokenAsFailed(paymentTokenData)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const customCheckoutYouthCartFlow = async (
  userId: string,
  orderId: string,
  amount: number,
  addressLine1: string,
  addressLine2: string,
  city: string,
  state: string,
  country: string,
  zip: string,
  shippingAddressLine1: string,
  shippingAddressLine2: string,
  shippingCity: string,
  shippingState: string,
  shippingCountry: string,
  shippingZip: string,
  token: string,
  useWalletBalance: boolean,
  amountToDebitFromWallet: number,
  isCardPayment: boolean,
  paymentInfo?: PaymentInfo,
  couponCode?: string,
  discountedAmout?: number,
) => {
  const conn = await connectToSalesforce()
 const paymentTokenData = await getActivePaymentToken(token)

  if (!paymentTokenData) {
    return responseGenerator(false, { errors: ['Invalid payment token'] })
  }

  const schedule = paymentTokenData.Schedule__c!
  const occurrences = paymentTokenData.Occurences__c.toString()
  const paymentType = paymentTokenData.Payment_Type__c

  let customerProfileId, paymentProfileId, ccNumber, ccExpiry, ccCode

  const userWalletBalance = await getWalletBalanceServer(userId)
  const effectiveAmount = useWalletBalance
    ? Math.max(0, amount - userWalletBalance)
    : amount
  const shouldChargeUser = effectiveAmount > 0

  if (shouldChargeUser) {
    if (isCardPayment) {
      const creditCardInfo = paymentInfo as CreditCardInfo
      ccNumber = creditCardInfo.ccNumber
      ccExpiry = creditCardInfo.ccExpiry
      ccCode = creditCardInfo.ccCode
    } else {
      const savedProfileInfo = paymentInfo as SavedProfileInfo
      customerProfileId = savedProfileInfo.customerProfileId
      paymentProfileId = savedProfileInfo.paymentProfileId
    }
  }

  try {
    //update order object with latest details
    await conn.sobject('Order').update({
      Id: orderId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
    })

    //create couponUsage records if couponCode is used
    if (couponCode && discountedAmout) {
      await createCouponUsageServer(userId, couponCode, discountedAmout)
    }

    //charge the credit card if the effective amount is greater than 0
    if (shouldChargeUser) {
      if (customerProfileId && paymentProfileId) {
        await chargeCustomerProfileByPaymentType(
          paymentType,
          customerProfileId,
          paymentProfileId,
          orderId,
          effectiveAmount,
          schedule,
          occurrences,
        )
      } else if (ccNumber && ccExpiry && ccCode) {
        await chargeCreditCardByPaymentType(
          paymentType,
          ccNumber,
          ccExpiry,
          ccCode,
          orderId,
          effectiveAmount,
          userId,
          schedule,
          occurrences,
        )
      } else {
        throw new Error('Invalid payment details')
      }
    }

    //create a transaction for wallet if the user has opted to use wallet balance
    if (useWalletBalance && amountToDebitFromWallet > 0) {
      const walletTransaction = {
        Account__c: userId,
        Amount__c: -amountToDebitFromWallet,
      }
      const walletTransactionObj = await conn
        .sobject('Transaction__c')
        .create(walletTransaction)

      //create a relavant payment object in salesforce for wallet
      const paymentObject = {
        Account__c: userId,
        Account_Number__c: userId,
        Account_Type__c: USER_WALLET,
        Amount__c: amountToDebitFromWallet,
        Order__c: orderId,
        Status__c: GatewayStatusEnum['1'],
        Transaction_Hash__c: hashString(
          walletTransactionObj.id + userId + orderId + amount,
        ),
        Transaction_ID__c: walletTransactionObj.id,
      }
      const paymentSFObject = await conn
        .sobject('Payment__c')
        .create(paymentObject)

      //associate wallet transaction with payment object
      await conn.sobject('Transaction__c').update({
        Id: walletTransactionObj.id!,
        Payment__c: paymentSFObject.id!,
      })
    }

    //update the payment token status to completed
    await markPaymentTokenAsCompleted(paymentTokenData)

    return responseGenerator(true, null)
  } catch (error) {
    await markPaymentTokenAsFailed(paymentTokenData)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const customSingleClassFlow = async (
  userId: string,
  entityId: string,
  amount: number,
  addressLine1: string,
  addressLine2: string,
  city: string,
  state: string,
  country: string,
  zip: string,
  shippingAddressLine1: string,
  shippingAddressLine2: string,
  shippingCity: string,
  shippingState: string,
  shippingCountry: string,
  shippingZip: string,
  token: string,
  useWalletBalance: boolean,
  amountToDebitFromWallet: number,
  isCardPayment: boolean,
  paymentInfo?: PaymentInfo,
  couponCode?: string,
  discountedAmout?: number,
  attendanceType?: string,
) => {
  const conn = await connectToSalesforce()
 const paymentTokenData = await getActivePaymentToken(token)

  if (!paymentTokenData) {
    return responseGenerator(false, { errors: ['Invalid payment token'] })
  }

  let customerProfileId, paymentProfileId, ccNumber, ccExpiry, ccCode

  const userWalletBalance = await getWalletBalanceServer(userId)
  const effectiveAmount = useWalletBalance
    ? Math.max(0, amount - userWalletBalance)
    : amount

  const shouldChargeUser = effectiveAmount > 0

  if (shouldChargeUser) {
    if (isCardPayment) {
      const creditCardInfo = paymentInfo as CreditCardInfo
      ccNumber = creditCardInfo.ccNumber
      ccExpiry = creditCardInfo.ccExpiry
      ccCode = creditCardInfo.ccCode
    } else {
      const savedProfileInfo = paymentInfo as SavedProfileInfo
      customerProfileId = savedProfileInfo.customerProfileId
      paymentProfileId = savedProfileInfo.paymentProfileId
    }
  }

  try {
    //create order object in salesforce
    const cartOrder = await conn.sobject('Order').create({
      Account__c: userId,
      AccountId: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
      Status: 'Draft',
      EffectiveDate: jsforce.SfDate.toDateLiteral(new Date()),
      Discount__c: discountedAmout,
    })

    //update account with latest billing and shipping address
    await conn.sobject('Account').update({
      Id: userId,
      BillingStreet: addressLine1 + ' ' + addressLine2,
      BillingCity: city,
      BillingState: state,
      BillingCountry: country,
      BillingPostalCode: zip,
      ShippingStreet: shippingAddressLine1 + ' ' + shippingAddressLine2,
      ShippingCity: shippingCity,
      ShippingState: shippingState,
      ShippingCountry: shippingCountry,
      ShippingPostalCode: shippingZip,
    })

    const orderId = cartOrder.id
    if (!orderId) {
      throw new Error('Failed to create order')
    }

    //get the product info for the class
    let soql = `Select Id,Name,Price__c,Family,Class__r.Type__c from Product2 where Class__c ='${entityId}'`
    const product = (await conn.query(soql)).records[0]

    if (!product) {
      throw new Error('class product not found')
    }

    //create a orderProduct
    const productId = product.Id!
    const pricebookId = await getPriceBookIdByProductServer(productId)
    const orderItem = {
      Product2Id: product.Id!,
      Account__c: userId,
      Quantity: 1,
      OrderId: orderId,
      UnitPrice: amount,
      Attendance_Type__c: attendanceType,
      PricebookEntryId: pricebookId,
    }
    await conn.sobject('OrderItem').create(orderItem)

    //create couponUsage records if couponCode is used
    if (couponCode && discountedAmout) {
      await createCouponUsageServer(userId, couponCode, discountedAmout)
    }

    //charge the credit card if the effective amount is greater than 0
    if (shouldChargeUser) {
      if (customerProfileId && paymentProfileId) {
        const chargeCustomerProfileResponse =
          await chargeCustomerProfileRequest(
            effectiveAmount,
            customerProfileId,
            paymentProfileId,
            orderId,
          )
      } else if (ccNumber && ccExpiry && ccCode) {
        const chargeCreditCardResponse = await createChargeCreditCardRequest(
          effectiveAmount,
          ccNumber,
          ccExpiry,
          ccCode,
          orderId,
        )
      } else {
        throw new Error('Invalid payment details')
      }
    }

    //create a transaction for wallet if the user has opted to use wallet balance
    if (useWalletBalance && amountToDebitFromWallet > 0) {
      const walletTransaction = {
        Account__c: userId,
        Amount__c: -amountToDebitFromWallet,
      }
      const walletTransactionObj = await conn
        .sobject('Transaction__c')
        .create(walletTransaction)

      //create a relavant payment object in salesforce for wallet
      const paymentObject = {
        Account__c: userId,
        Account_Number__c: userId,
        Account_Type__c: USER_WALLET,
        Amount__c: amountToDebitFromWallet,
        Order__c: orderId,
        Status__c: GatewayStatusEnum['1'],
        Transaction_Hash__c: hashString(
          walletTransactionObj.id + userId + orderId + amount,
        ),
        Transaction_ID__c: walletTransactionObj.id,
      }
      const paymentSFObject = await conn
        .sobject('Payment__c')
        .create(paymentObject)

      //associate wallet transaction with payment object
      await conn.sobject('Transaction__c').update({
        Id: walletTransactionObj.id!,
        Payment__c: paymentSFObject.id!,
      })
    }

    //update the payment token status to completed
    await markPaymentTokenAsCompleted(paymentTokenData)

    return responseGenerator(true, null)
  } catch (error) {
    await markPaymentTokenAsFailed(paymentTokenData)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const createCustomCheckoutRequest = async (
  paymentToken: string,
  addressLine1: string,
  addressLine2: string,
  city: string,
  state: string,
  country: string,
  zip: string,
  shippingAddressLine1: string,
  shippingAddressLine2: string,
  shippingCity: string,
  shippingState: string,
  shippingCountry: string,
  shippingZip: string,
  useWalletBalance: boolean,
  agreeToPayProcessingFee: boolean,
  paymentInfo?: PaymentInfo,
) => {
  try {
    const decodedPaymentToken = verifyPaymentToken(paymentToken)
    if (decodedPaymentToken == null) {
      return responseGenerator(false, { errors: ['Invalid payment token'] })
    }

    const paymentTokenData = await getActivePaymentToken(paymentToken)

    if (!paymentTokenData) {
      return responseGenerator(false, { errors: ['Invalid payment token'] })
    }

    //if the paymentToken is more than 5 mins old, then it is invalid
    if (
      decodedPaymentToken.entityType !== 'Youth Cart' &&
      differenceInMinutes(new Date(), paymentTokenData.CreatedDate) > 5
    ) {
      await markPaymentTokenAsFailed(paymentTokenData)
      return responseGenerator(false, { errors: ['Payment token expired'] })
    }

    //if it is a youth cart payment, and payment token is 7 days old, then it is invalid
    if (
      decodedPaymentToken.entityType === 'Youth Cart' &&
      differenceInDays(new Date(), paymentTokenData.CreatedDate) > 7
    ) {
      await markPaymentTokenAsFailed(paymentTokenData)
      return responseGenerator(false, { errors: ['Payment token expired'] })
    }

    const isCardPayment = !!(
      paymentInfo && paymentInfo.hasOwnProperty('ccNumber')
    )

    const {
      userId,
      entityId,
      entityType,
      discountedAmount,
      appliedCoupon,
      isDonationAnonymous,
      attendanceType,
    } = decodedPaymentToken
    let { Amount__c: amount } = paymentTokenData

    if (isCardPayment) {
      //save the credit card details if the user has opted to save the card
      const creditCardInfo = paymentInfo as CreditCardInfo
      if (creditCardInfo.saveCard) {
        const { customerProfileIdUser, profileExists } =
          await getOrCreateCustomerProfileServer(
            decodedPaymentToken.userId,
            creditCardInfo.ccNumber,
            creditCardInfo.ccExpiry,
            creditCardInfo.ccCode,
          )
        if (profileExists) {
          await createCustomerPaymentProfileServer(
            userId,
            customerProfileIdUser,
            creditCardInfo.ccNumber,
            creditCardInfo.ccExpiry,
            creditCardInfo.ccCode,
          )
        }
      }
    }

    const userWalletBalance = await getWalletBalanceServer(userId)

    const amountToDebitFromWallet = useWalletBalance
      ? Math.min(amount, userWalletBalance)
      : 0

    const processingFee = calculateProcessingFee(amount)
    amount = agreeToPayProcessingFee ? amount + processingFee : amount

    switch (entityType) {
      case 'Donation':
        return customCheckoutDonationFlow(
          userId,
          entityId,
          amount,
          addressLine1,
          addressLine2,
          city,
          state,
          country,
          zip,
          shippingAddressLine1,
          shippingAddressLine2,
          shippingCity,
          shippingState,
          shippingCountry,
          shippingZip,
          paymentToken,
          useWalletBalance,
          amountToDebitFromWallet,
          isCardPayment,
          isDonationAnonymous,
          paymentInfo,
        )

      case 'Cart':
        return customCheckoutCartFlow(
          userId,
          amount,
          addressLine1,
          addressLine2,
          city,
          state,
          country,
          zip,
          shippingAddressLine1,
          shippingAddressLine2,
          shippingCity,
          shippingState,
          shippingCountry,
          shippingZip,
          paymentToken,
          useWalletBalance,
          amountToDebitFromWallet,
          isCardPayment,
          paymentInfo,
          decodedPaymentToken.appliedCoupon,
          decodedPaymentToken.discountedAmount,
        )

      case 'Youth Cart':
        return customCheckoutYouthCartFlow(
          userId,
          entityId,
          amount,
          addressLine1,
          addressLine2,
          city,
          state,
          country,
          zip,
          shippingAddressLine1,
          shippingAddressLine2,
          shippingCity,
          shippingState,
          shippingCountry,
          shippingZip,
          paymentToken,
          useWalletBalance,
          amountToDebitFromWallet,
          isCardPayment,
          paymentInfo,
          decodedPaymentToken.appliedCoupon,
          decodedPaymentToken.discountedAmount,
        )

      case 'Wallet':
        return customCheckoutWalletFlow(
          userId,
          amount, //user cannot use wallet balance to pay for wallet
          paymentToken,
          isCardPayment,
          paymentInfo,
        )

      case 'Invoice':
        return customCheckoutInvoiceFlow(
          userId,
          entityId,
          amount,
          addressLine1,
          addressLine2,
          city,
          state,
          country,
          zip,
          shippingAddressLine1,
          shippingAddressLine2,
          shippingCity,
          shippingState,
          shippingCountry,
          shippingZip,
          paymentToken,
          false, //cannot use wallet balance in invoice flow
          amountToDebitFromWallet,
          isCardPayment,
          isDonationAnonymous,
          paymentInfo,
        )

      case 'Subscription':
        return customCheckoutSubscriptionFlow(
          userId,
          entityId,
          amount,
          addressLine1,
          addressLine2,
          city,
          state,
          country,
          zip,
          shippingAddressLine1,
          shippingAddressLine2,
          shippingCity,
          shippingState,
          shippingCountry,
          shippingZip,
          paymentToken,
          false, //cannot use wallet balance in invoice flow
          amountToDebitFromWallet,
          isCardPayment,
          isDonationAnonymous,
          paymentInfo,
        )
      case 'Single Class':
        return customSingleClassFlow(
          userId,
          entityId,
          amount,
          addressLine1,
          addressLine2,
          city,
          state,
          country,
          zip,
          shippingAddressLine1,
          shippingAddressLine2,
          shippingCity,
          shippingState,
          shippingCountry,
          shippingZip,
          paymentToken,
          useWalletBalance,
          amountToDebitFromWallet,
          isCardPayment,
          paymentInfo,
          decodedPaymentToken.appliedCoupon,
          decodedPaymentToken.discountedAmount,
          attendanceType,
        )
      default:
        return responseGenerator(false, { errors: ['Invalid payment token'] })
    }
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}
