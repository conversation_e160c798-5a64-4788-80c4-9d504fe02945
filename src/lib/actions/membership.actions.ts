'use server'

import jsforce from 'jsforce'

import { getAccountId } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { getWalletBalanceServer } from '@/lib/actions/wallet.actions'
import { createPaymentToken, DecodedPaymentToken } from '@/lib/jwt'
import { connectToSalesforce } from '@/lib/salesforce'
import PaymentTokenModel, { IPaymentToken } from '@/models/paymentToken.model'
import { responseGenerator } from '@/utils/utils'
import { connectToMongoDB } from '@/lib/db'
import { createCancelRecurringRequest } from './payment.actions'

export const getActiveMembershipByUserIdServer = async (userId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id,Product__r.Id, Order__c FROM Membership__c WHERE Account__c = '${userId}' AND Expires_On__c > ${jsforce.SfDate.TODAY}`
  const records = (await conn.query(soql)).records

  return records
}

export const getSubscriptionIdFromOrderIdOrThrow = async (orderId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Recurring_Payment_ID__c FROM Order WHERE Id = '${orderId}'`
  const records = (await conn.query(soql)).records

  if (records.length === 0) {
    throw new Error('Invalid Order')
  }

  const order = records[0]
  const subId = order['Recurring_Payment_ID__c']
  if (!subId) {
    throw new Error('Active subscription not found')
  }

  return subId as string
}

export const getMembershipProductDetailByIdOrThrow = async (
  productId: string,
) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id,Monthly_Minimum__c, Yearly_Minimum__c FROM Product2 WHERE Family ='Membership' AND Id='${productId}'`
  const records = (await conn.query(soql)).records
  if (records.length === 0) {
    throw new Error('Incorrect Membership selected')
  }
  return records[0]
}

export const getBenefitsForMembershipProductServer = async (
  productId: string,
) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Description__c FROM Benefit__c WHERE  Product__c = '${productId}'`
  const records = (await conn.query(soql)).records
  return records
}

export const getUserActiveMembership = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const activeMemberships =
      await getActiveMembershipByUserIdServer(accessTokenUserId)
    if (activeMemberships.length === 0) {
      throw new Error('No active membership found')
    }
    return responseGenerator(true, activeMemberships[0])
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const getMembershipProducts = async (accessToken?: string) => {
  try {
    const conn = await connectToSalesforce()
    const soql = `SELECT Id, Name, Monthly_Minimum__c, Yearly_Minimum__c,Description FROM Product2 WHERE Family = 'Membership' AND IsActive = true`
    const records = (await conn.query(soql)).records

    const productRecords = await Promise.all(
      records.map(async (item) => {
        const benefits = await getBenefitsForMembershipProductServer(item.Id!)
        return {
          ...item,
          benefits,
        }
      }),
    )

    let activeMembership = null
    if (accessToken) {
      if (!(await verifyCustomAccountSession(accessToken))) {
        return responseGenerator(false, { errors: ['Unauthorized'] }, true)
      }
      const accessTokenUserId = await getAccountId(accessToken)

      if (!accessTokenUserId) {
        return responseGenerator(false, { errors: ['Unauthorized'] }, true)
      }

      const activeMemberships =
        await getActiveMembershipByUserIdServer(accessTokenUserId)
      activeMembership =
        activeMemberships?.length > 0 ? activeMemberships[0] : null
    }
    const returnData = {
      productRecords,
      activeMembership,
    }
    return responseGenerator(true, returnData)
  } catch (error) {
    console.error('Error fetching membership products:', error)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const purchaseMembership = async (
  accessToken: string,
  productId: string,
  type: 'Monthly' | 'Annual',
) => {
  try {
    if (!['Monthly', 'Annual'].includes(type)) {
      throw new Error('Recurrence should be monthly or Annual')
    }
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    await connectToMongoDB()

    const productDetail = await getMembershipProductDetailByIdOrThrow(productId)
    const monthlyPrice = productDetail['Monthly_Minimum__c']
    const yearlyPrice = productDetail['Yearly_Minimum__c']

    const amount = type === 'Monthly' ? monthlyPrice : yearlyPrice
    const userWalletBalance = await getWalletBalanceServer(accessTokenUserId)

    const paymentTokenData: DecodedPaymentToken = {
      userId: accessTokenUserId,
      entityId: productId,
      entityType: 'Subscription',
      amount,
      walletBalance: userWalletBalance,
      paymentType: 'Subscription',
      isWalletEnabled: false,
    }

    const paymentJwtToken = createPaymentToken(paymentTokenData)

    //create a new payment token for the user
    const paymentToken: IPaymentToken = {
      userId: accessTokenUserId,
      token: paymentJwtToken,
      status: 'active',
      amount: amount,
      paymentType: 'Subscription',
      schedule: type,
      occurrences: 9999, //9999 for ongoing subscription
    }

    await connectToMongoDB()
    await PaymentTokenModel.create(paymentToken)

    const returnData = {
      token: paymentJwtToken,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const cancelActiveMembership = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }
    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const activeMemberships =
      await getActiveMembershipByUserIdServer(accessTokenUserId)
    if (activeMemberships.length === 0) {
      throw new Error('No active membership')
    }
    const currentMembership = activeMemberships[0]
    const orderId = currentMembership['Order__c']

    const subscriptionId = await getSubscriptionIdFromOrderIdOrThrow(orderId)
    const authorizenetResponse = await createCancelRecurringRequest(
      subscriptionId,
      orderId,
    )

    if (authorizenetResponse.messages.resultCode !== 'Ok') {
      throw new Error('Failed to cancel membership')
    }

    responseGenerator(true, null)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}
