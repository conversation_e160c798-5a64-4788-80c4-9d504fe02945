import { PaymentType } from '@/utils/constants'
import jwt from 'jsonwebtoken'

export type DecodedPaymentToken = {
  entityId: string
  entityType:
    | 'Donation'
    | 'Cart'
    | 'Wallet'
    | 'Invoice'
    | 'Subscription'
    | 'Youth Cart'
    | 'Single Class'
  userId: string
  amount: number
  walletBalance: number
  paymentType: PaymentType
  parentAccountId?: string
  appliedCoupon?: string
  discountedAmount?: number
  isDonationAnonymous?: boolean
  attendanceType?: string
  isWalletEnabled: boolean
}

const paymentSecret = process.env.PAYMENT_TOKEN_SECRET as string

export const createPaymentToken = (token: DecodedPaymentToken) => {
  return jwt.sign(token, paymentSecret)
}

export const verifyPaymentToken = (
  token: string,
): DecodedPaymentToken | null => {
  try {
    return jwt.verify(token, paymentSecret) as DecodedPaymentToken
  } catch (e) {
    console.error('verifyPaymentToken', e)
    return null
  }
}

export const decodePaymentToken = (token: string): DecodedPaymentToken => {
  return jwt.decode(token) as DecodedPaymentToken
}
