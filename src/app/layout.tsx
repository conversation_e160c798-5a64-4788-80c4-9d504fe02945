import type { Metadata } from 'next'
import { StackProvider, StackTheme } from '@stackframe/stack'
import { Plus_Jakarta_Sans } from 'next/font/google'
import './globals.css'
import Head from 'next/head'
import Header from '@/components/Header/Header'
import React from 'react'
import { ToastContainer } from 'react-toastify'
import { connectToMongoDB } from '@/lib/db'
import 'react-toastify/dist/ReactToastify.css'
import { AuthProvider } from '@/contexts/AuthContext'
import { stackServerApp } from '@/stack'
import { Providers } from '@/lib/Providers'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import Footer from '@/components/Footer/Footer'
import { getNavigationTree } from '@/lib/actions/navbar.actions'
import LayoutWrapper from '@/components/LayoutWrapper/LayoutWrapper'
import { headers } from 'next/headers'

const jakartaSans = Plus_Jakarta_Sans({ subsets: ['latin'] })

export async function generateMetadata(): Promise<Metadata> {
  const pathname = headers().get('x-pathname') || '/'

  const routeTitles: Record<string, string> = {
    '/': 'Home - The Muse Writers Center',
    '/dashboard': 'Dashboard - The Muse Writers Center',
    '/sign-in': 'Sign In - The Muse Writers Center',
    '/cart': 'My Cart - The Muse Writers Center',
    '/settings': 'Settings - The Muse Writers Center',
    '/donations': 'Donations - The Muse Writers Center',
  }

  const title = routeTitles[pathname] || 'The Muse Writers Center'

  return {
    title: `${title}`,
  }
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const [userData, headerData] = await Promise.all([
    handleLoggedIn(),
    getNavigationTree(),
  ])
  const role = userData?.role
  const theme = {
    light: {
      primary: '#FFBB54',
      primaryForeground: 'black',
    },
    radius: '8px',
  }
  return (
    <html lang="en">
      <body className={jakartaSans.className}>
        <StackProvider app={stackServerApp}>
          <StackTheme theme={theme}>
            <Providers>
              <AuthProvider>
                <Head>
                  <link
                    rel="stylesheet"
                    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap"
                  />
                </Head>
                <LayoutWrapper role={role}>
                  <Header userData={userData} headerData={headerData} />
                  {children}
                  <Footer role={role} />
                </LayoutWrapper>
                <ToastContainer />
              </AuthProvider>
            </Providers>
          </StackTheme>
        </StackProvider>
      </body>
    </html>
  )
}
