import PageHeader from '@/components/PageHeader/PageHeader'
import AccountSettingsTabs from './AccountSettingsTabs'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { ROLES } from '@/types/enum'

const page = async () => {
  const userData = await handleLoggedIn()
  return (
    <div
      className={`w-full ${!userData && 'flex flex-col items-center justify-center'}`}
    >
      {userData && (
        <PageHeader
          heading="Account"
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={userData?.role !== ROLES.Teacher}
          userData={userData}
        />
      )}
      <AccountSettingsTabs userData={userData} />
    </div>
  )
}

export default page
