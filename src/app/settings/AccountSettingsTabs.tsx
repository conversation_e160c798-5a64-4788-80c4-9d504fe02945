'use client'

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { useSearchParams } from 'next/navigation'

import Edit<PERSON>erson from '@/components/Icons/EditPerson'
import BellIcon from '@/components/Icons/BellIcon'
import PaymentMethod from '@/components/Icons/PaymentMethod'
import Wallet from '@/components/Icons/Wallet'
import Help from '@/components/Icons/Help'
import LogoutButton from '@/components/LogoutButton/LogoutButton'
import SettingIcon from '@/components/Icons/SettingIcon'
import Membership from '@/components/Icons/Membership'
import MembershipPlans from '@/components/MembershipPlans/MembershipPlans'
import { ROLES } from '@/types/enum'

const ProfileSetting = dynamic(
  () => import('@/components/ProfileSetting/ProfileSetting'),
  { ssr: false },
)
const NotificationsSetting = dynamic(
  () => import('@/components/NotificationsSetting/NotificationsSetting'),
  { ssr: false },
)
const PaymentSettings = dynamic(
  () => import('@/components/PaymentSettings/PaymentSettings'),
  { ssr: false },
)
const AccountSettings = dynamic(
  () => import('@/components/AccountSettings/AccountSettings'),
  { ssr: false },
)
const HelpdeskSetting = dynamic(
  () => import('@/components/HelpdeskSetting/HelpdeskSetting'),
  { ssr: false },
)
const CreditDetails = dynamic(
  () => import('@/components/CreditDetails/CreditDetails'),
  { ssr: false },
)

const accountLinks = [
  {
    title: 'Edit Profile',
    component: 'ProfileSetting',
    icon: (isActive: boolean) => <EditPerson isActive={isActive} />,
  },
  {
    title: 'Membership',
    component: 'Membership',
    icon: (isActive: boolean) => <Membership isActive={isActive} />,
  },
  {
    title: 'Wallet',
    component: 'AddMoneyToWallet',
    icon: (isActive: boolean) => <Wallet isActive={isActive} />,
  },
  {
    title: 'Notification Settings',
    component: 'NotificationsSetting',
    icon: (isActive: boolean) => <BellIcon isActive={isActive} />,
  },
  {
    title: 'Payment Methods',
    component: 'PaymentSettings',
    icon: (isActive: boolean) => <PaymentMethod isActive={isActive} />,
  },
  {
    title: 'Account Settings',
    component: 'AccountSettings',
    icon: (isActive: boolean) => <SettingIcon isActive={isActive} />,
  },
  {
    title: 'Helpdesk',
    component: 'HelpdeskSetting',
    icon: (isActive: boolean) => <Help isActive={isActive} />,
  },
]

function getTabComponent(tab: string, userData: any) {
  switch (tab) {
    case 'ProfileSetting':
      return <ProfileSetting userData={userData} />
    case 'Membership':
      return <MembershipPlans />
    case 'AddMoneyToWallet':
      return <CreditDetails />
    case 'NotificationsSetting':
      return <NotificationsSetting userData={userData} />
    case 'PaymentSettings':
      return <PaymentSettings />
    case 'AccountSettings':
      return <AccountSettings />
    case 'HelpdeskSetting':
      return <HelpdeskSetting />
    default:
      return (
        <div className="bg-white rounded-xl p-8 shadow-sm">
          <h1 className="text-2xl font-bold mb-4">
            Welcome to your account settings
          </h1>
          <p className="text-gray-600">
            Select an option from the left to manage your account.
          </p>
        </div>
      )
  }
}

export default function AccountSettingsTabs({ userData }: { userData: any }) {
  const searchParams = useSearchParams()
  const tabParam = searchParams.get('tab')
  const [activeTab, setActiveTab] = useState(
    tabParam || accountLinks[0].component,
  )

  useEffect(() => {
    if (tabParam && tabParam !== activeTab) {
      setActiveTab(tabParam)
    }
  }, [tabParam])

  const handleTabClick = (component: string) => {
    setActiveTab(component)
    const params = new URLSearchParams(window.location.search)
    params.set('tab', component)
    window.history.replaceState(
      {},
      '',
      `${window.location.pathname}?${params.toString()}`,
    )
    window.scrollTo(0, 0)
  }

  return (
    <div className="flex justify-center gap-8 w-full p-8 min-h-screen bg-gray-50">
      {userData && userData?.role !== ROLES.Teacher && (
        <aside className="w-80 h-max border-r bg-white rounded-xl p-6 flex flex-col justify-between sticky top-4">
          <div>
            <h2 className="font-bold text-lg mb-6">
              Update And Manage Your Account
            </h2>
            <nav className="flex flex-col gap-3">
              {accountLinks.map((link) => {
                const isActive = activeTab === link.component
                return (
                  <button
                    key={link.component}
                    onClick={() => handleTabClick(link.component)}
                    className={`flex items-center text-base gap-3 px-4 py-3 rounded-lg text-left font-medium transition ${isActive ? 'bg-gray-100' : 'hover:bg-gray-50'} ${isActive ? 'text-black' : 'text-gray-700'}`}
                    style={{ border: 'none' }}
                  >
                    <span className="w-6 h-6 flex items-center justify-center">
                      {link.icon(isActive)}
                    </span>
                    {link.title}
                  </button>
                )
              })}
            </nav>
          </div>
          <div className="mt-8">
            <LogoutButton />
          </div>
        </aside>
      )}
      <main
        className={`${userData && userData?.role === ROLES.Teacher ? 'w-1/2' : 'flex-1'}`}
      >
        <div className="bg-white rounded-xl p-8 shadow-sm min-h-[calc(100vh-4rem)]">
          {getTabComponent(activeTab, userData)}
        </div>
      </main>
    </div>
  )
}
