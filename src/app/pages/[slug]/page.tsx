import DynamicForm from '@/components/DynamicForm/DynamicForm'
import { getFormById } from '@/lib/actions/form.actions'
import { getPageContent } from '@/lib/actions/page.actions'
import React from 'react'
import { toast } from 'react-toastify'

interface PageProps {
  params: {
    slug: string
  }
}

const fetchFormData = async (formId: string) => {
  if (!formId) return
  try {
    const formData = await getFormById(formId)
    return JSON.parse(formData)?.formData
  } catch (error) {
    toast.error('Error fetching form' + error)
  }
  return null
}

const page = async ({ params }: PageProps) => {
  const pageData: any = await getPageContent(params?.slug)
  const formData = await fetchFormData(pageData?.Form_ID__c)
  return (
    <div className="w-3/4 py-10 mx-auto">
      <h1 className="font-bold text-5xl">{pageData?.Title__c}</h1>
      <div
        className="mt-10
                "
        dangerouslySetInnerHTML={{
          __html: pageData?.Content__c,
        }}
      />
      {formData && (
        <div className="w-full flex items-start mt-10">
          <div className="w-1/2">
            <DynamicForm id={pageData?.Form_ID__c!} data={formData} />
          </div>
        </div>
      )}
    </div>
  )
}

export default page
