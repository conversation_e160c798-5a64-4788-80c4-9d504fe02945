'use client'
import React from 'react'
import { useRouter } from 'next/navigation'
import PaymentSuccessIcon from '@/components/Icons/PaymentSuccessIcon'
import { useSelector } from 'react-redux'
import { paymentToken } from '@/lib/redux'

export default function Page() {
  const router = useRouter()
  const token = useSelector(paymentToken)

  // Only redirect if no token after a short delay (allows for token to be set)
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (!token) {
        router.push('/dashboard')
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [token, router])

  return (
    <div className="flex item-center justify-center py-20 w-full h-[100%]">
      <div className="flex flex-col justify-center items-center">
        <PaymentSuccessIcon />
        <h1 className="font-extrabold text-3xl mt-5 mb-3">
          Payment Successful!
        </h1>
        <p className="text-sm">
          Thank you for choosing us! We&#39;ve received your payment.
        </p>
        <p className="text-sm">
          {' '}
          Please check your email for the receipt and confirmation details.
        </p>
        <div className="flex items-center gap-3 text-sm mt-3 font-semibold">
          <button
            className="bg-color-yellow py-4 px-6 rounded-md h-12 flex items-center justify-center"
            onClick={() => router.push('/dashboard')}
          >
            Return to Home
          </button>
          <button className="border-color-grey-2 border py-4 px-6 rounded-md h-12 flex items-center justify-center">
            Download Receipt
          </button>
        </div>
        <p className="text-xs mt-32">
          Need help or have questions? Reach out to our support team.
        </p>
      </div>
    </div>
  )
}
