import SubscriptionPage from '@/components/SubscriptionPage/SubscriptionPage'
import { getToken } from '@/lib/actions/login.actions'
import { getMembershipProducts } from '@/lib/actions/membership.actions'

const Page = async () => {
  const getProducts = await getMembershipProducts()
  const productArray = getProducts?.data?.productRecords || []

  const sortedProductArray = [...productArray].sort(
    (a, b) => (a?.Monthly_Minimum__c || 0) - (b?.Monthly_Minimum__c || 0),
  )
  const structuredProductArray = sortedProductArray?.map(
    (item: any, index: number) => {
      return {
        Id: item?.Id,
        name: item?.Name,
        annualPrice: item?.Yearly_Minimum__c,
        price: item?.Monthly_Minimum__c,
        cta: `Join the ${item?.Name}`,
        features: item?.benefits?.map((benefits: any) => {
          return { label: benefits?.Description__c, included: true }
        }),
        highlight: index === 1,
        dark: index === 1,
        annualSavings:
          item?.Monthly_Minimum__c && item?.Yearly_Minimum__c
            ? Math.round(
                ((item.Monthly_Minimum__c * 12 - item.Yearly_Minimum__c) /
                  (item.Monthly_Minimum__c * 12)) *
                  100,
              )
            : 0,
      }
    },
  )
  return <SubscriptionPage products={structuredProductArray} />
}

export default Page
