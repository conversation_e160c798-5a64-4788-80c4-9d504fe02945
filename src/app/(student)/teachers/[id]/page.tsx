import React from 'react'
import <PERSON>Header from '@/components/PageHeader/PageHeader'
import <PERSON>Arrow from '@/components/Icons/BackArrow'
import TeacherDetail from '@/components/TeacherDetail/TeacherDetail'
import {
  getAllTeacherMeetingsById,
  getTeacherDetailsById,
} from '@/lib/actions/teacher.actions'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { getClasses } from '@/lib/actions/class.actions'
import dayjs from 'dayjs'
import { cache } from 'react'

interface PageProps {
  params: {
    id: string
  }
}

const getTeacherDetailsCached = cache(async (teacherId: string) => {
  return await getTeacherDetailsById(teacherId)
})

export async function generateMetadata({ params }: PageProps) {
  const teacher = await getTeacherDetailsCached(params.id)
  const title = teacher?.data?.Name || 'Teacher Details'

  return {
    title: `${title} - The Muse Writers Center`,
  }
}

const Page = async ({ params }: PageProps) => {
  const startDate = dayjs().format('YYYY-MM-DD')
  const endDate = dayjs().endOf('month').format('YYYY-MM-DD')

  const [userData, teacher, teacherMeetings, previousClasses] =
    await Promise.all([
      handleLoggedIn(),
      getTeacherDetailsCached(params?.id),
      getAllTeacherMeetingsById(params?.id, startDate, endDate),
      getClasses(
        '',
        'Previous',
        'All',
        undefined,
        undefined,
        undefined,
        undefined,
        params?.id,
      ),
    ])

  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Teacher Details'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <div className="px-6 pt-6 pb-2 max-w-6xl flex items-center">
        <BackArrow />
      </div>
      <TeacherDetail
        teacher={teacher}
        teacherMeetings={teacherMeetings}
        previousClasses={previousClasses}
      />
    </div>
  )
}

export default Page
