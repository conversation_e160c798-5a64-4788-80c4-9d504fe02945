import PaymentForm from '@/components/PaymentForm/PaymentForm'
import React from 'react'
import jwt from 'jsonwebtoken'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { PriceDetails } from '@/utils/interface'

interface PageProps {
  searchParams: { token?: string }
}

const Page = async ({ searchParams }: PageProps) => {
  const userData = await handleLoggedIn()

  let paymentDetails: PriceDetails = {
    userId: '',
    entityId: '',
    entityType: '',
    amount: 0,
    walletBalance: 0,
    iat: 0,
    discountedAmount: 0,
    paymentType: '',
  }

  if (searchParams?.token) {
    paymentDetails =
      (jwt.decode(searchParams.token) as PriceDetails) || paymentDetails
  }

  return (
    <>
      <div></div>
      <div className="w-full flex justify-center">
        <div className="w-11/12 md:w-9/12 mt-5">
          <PaymentForm data={paymentDetails} userData={userData} />
        </div>
      </div>
    </>
  )
}

export default Page
