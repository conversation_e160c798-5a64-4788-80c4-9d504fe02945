import ClassesPage from '@/components/ClassListing/ClassListing'
import PageHeader from '@/components/PageHeader/PageHeader'
import {
  getClassesGenreFilters,
  getClassesLevelFilters,
  getClassesTypeFilters,
} from '@/lib/actions/class.actions'
import { handleLoggedIn } from '@/lib/actions/login.actions'
import { ROLES } from '@/types/enum'
import React from 'react'

export default async function Page() {
  const [userData, genres, types, levels] = await Promise.all([
    handleLoggedIn(),
    getClassesGenreFilters(),
    getClassesTypeFilters(),
    getClassesLevelFilters(),
  ])

  return (
    <div
      className={`w-full ${!userData && 'flex flex-col items-center justify-center'}`}
    >
      {userData && (
        <PageHeader
          heading="My Bookings"
          showTittle={false}
          showSearch={false}
          showMessage={false}
          showCart={userData?.role !== ROLES.Teacher}
          userData={userData}
        />
      )}
      <ClassesPage
        genres={genres?.data ?? []}
        types={types?.data ?? []}
        levels={levels?.data ?? []}
        userData={userData}
        selfClasses={true}
      />
    </div>
  )
}
