import PageHeader from '@/components/PageHeader/PageHeader'
import React from 'react'
import {
  getEventCategoryFilters,
  getEvents,
  getSubcategoryDropdownValues,
} from '@/lib/actions/class.actions'
import EventsPage from '@/components/EventsPage/EventsPage'
import { handleLoggedIn } from '@/lib/actions/login.actions'

const Page = async () => {
  const [userData, eventType] = await Promise.all([
    handleLoggedIn(),
    getEventCategoryFilters(),
  ])
  return (
    <div className="w-screen md:w-full">
      {userData && (
        <PageHeader
          heading={'Events'}
          showTittle={false}
          showSearch={false}
          showMessage={false}
          userData={userData}
        />
      )}
      <EventsPage userData={userData} eventType={eventType?.data || []} />
    </div>
  )
}

export default Page
