@tailwind base;
@tailwind components;
@tailwind utilities;

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

html,
body {
    background-color: #F5F5F7;
    height: 100%;
}

.layout-container {
    display: grid;
    gap: 2px;
}

.two-column {
    grid-template-columns: auto 1fr;
}

.one-column {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
}

/* Responsive variant */
@media (max-width: 1023px) {
    .layout-container.two-column {
      display: block; /* fallback to one column on small screens */
    }
  }

.modal-wrapper {
    max-width: 600px;
    height: 600px;
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: #888 #e0e0e0;
}

.modal-wrapper::-webkit-scrollbar {
    width: 12px;
}

.modal-wrapper::-webkit-scrollbar-track {
    background: #e0e0e0;
}

.modal-wrapper::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;

}

.modal-wrapper::-webkit-scrollbar-thumb:hover {
    background: #555;
}

form.stack-scope button {
    background-color: #FFBB54 !important;
    color: black !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

.stdropdown-input.stsearch-box input {
    padding: 0 5px !important;
    border: none !important;
    border-radius: 8px;
}

.stdropdown-input.stsearch-box input:focus-visible {
    outline: none !important;
    border: none !important;
}

.loader-spinner {
    width: 1.25rem;
    height: 1.25rem;
    border-width: 2px;
    border-style: solid;
    border-color: grey;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Loader Spinner */
@keyframes barWidth {
    0% {
        width: 0%;
    }

    25% {
        width: 50%;
    }

    50% {
        width: 100%;
    }

    75% {
        width: 50%;
    }

    100% {
        width: 0%;
    }
}

@keyframes barWidth2 {
    0% {
        width: 0%;
    }

    50% {
        width: 50%;
    }

    100% {
        width: 100%;
    }
}

.horizontal-bar-wrap {
    height: 4px;
    width: 100%;

    .bar {
        position: relative;
        width: 0%;
        height: 100%;
        margin: 0 auto;
        animation: barWidth;
        animation-duration: 2s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;

        &.bar1 {
            animation-delay: 0s;
            background: #FFD25A;
            top: 0;
            z-index: 1;
        }
    }
}


.success-loader {
    width: 200px;
    aspect-ratio: 1;
    border-radius: 50%;
    padding: 6px;
    position: relative;
}

/* HTML: <div class="loader"></div> */
.rotating-background {
    width: 190px;
    aspect-ratio: 1;
    border-radius: 50%;
    padding: 6px;
    background:
        conic-gradient(from 135deg at top, #0474F4 90deg, #0000 0) 0 calc(50% - 4px)/17px 8.5px,
        radial-gradient(farthest-side at bottom left, #0000 calc(100% - 6px), #0474F4 calc(100% - 5px) 99%, #0000) top right/50% 50% content-box content-box,
        radial-gradient(farthest-side at top, #0000 calc(100% - 6px), #0474F4 calc(100% - 5px) 99%, #0000) bottom /100% 50% content-box content-box;
    background-repeat: no-repeat;
    animation: l11 2s infinite linear;
}

@keyframes l11 {
    100% {
        transform: rotate(1turn)
    }
}


/*  CSS: Delete Icon loader */
.delete-icon-loader {
    width: 10px;
    aspect-ratio: 1;
    border-radius: 50%;
    border: 2px solid #0000;
    border-right-color: #ffa50097;
    position: relative;
    animation: l24 1s infinite linear;
}

.delete-icon-loader:before,
.delete-icon-loader:after {
    content: "";
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    border: inherit;
    animation: inherit;
    animation-duration: 2s;
}

.delete-icon-loader:after {
    animation-duration: 4s;
}

@keyframes l24 {
    100% {
        transform: rotate(1turn)
    }
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
}

.home-buttons:hover>a:not(:hover)>div {
    background-color: #FFF8E5;
}

.infinite-scroll-component__outerdiv {
    width: 100%;
}

.react-datepicker__input-container {
    height: 100%;
}

.stdropdown-container {
    border: 1px solid #DFDFDF !important;
}

.teacher-onboarding .stdropdown-input.stsearch-box {
    padding: 0;
}

.teacher-onboarding .stdropdown-input.stsearch-box {
    background-color: #FFF;
    height: 41px;
    border-radius: 8px;
    padding: 3px 10px;
}

/* In your global CSS or component stylesheet */
@layer components {
    .week-view-container {
        overflow-x: hidden;
        max-width: 100%;
    }

    .event-card {
        min-width: 0;
        /* Prevents cards from overflowing their containers */
        width: 100%;
    }
}

@media (max-width: 640px) {
    .week-day {
        padding: 0.5rem;
    }
}

.shimmer {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(90deg,
            #e5e7eb 25%,
            /* gray-200 */
            #f3f4f6 50%,
            /* gray-100 */
            #e5e7eb 75%
            /* gray-200 */
        );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

/**
 * React Datepicker
 */
.react-datepicker-wrapper {
    width: 100%;
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.animate-shimmer {
    background: linear-gradient(90deg, #9ca3af 25%, #d1d5db 50%, #9ca3af 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}