export enum ROLES {
  Youth_Student = 'Youth Student',
  Adult_Student = 'Adult Student',
  Teacher = 'Teacher',
  Volunteer = 'Volunteer',
  Staff = 'Staff',
  Board_Member = 'Board Member',
  Committee_Member = 'Committee Member',
  Donor = 'Donor',
  Emeritus_Board_Member = 'Emeritus Board Member',
  Member = 'Member',
  Newsletter_Subscriber = 'Newsletter Subscriber',
  Parent = 'Parent',
}

export enum EVENT_TYPES {
  Artist_Event = 'Artist Event',
  Book_Launch = 'Book Launch',
  Club = 'Club',
  Drop_in_Writing = 'Drop-in Writing',
  Festival = 'Festival',
  Film_Screening = 'Film Screening',
  Free_Class = 'Free Class',
  Fundraiser = 'Fundraiser',
  Music = 'Music',
  Networking = 'Networking',
  Open_Mic = 'Open Mic',
  Panel = 'Panel',
  Party_Reception = 'Party/Reception',
  Play = 'Play',
  Poetry_Submission = 'Poetry Submission',
  Private_Group = 'Private Group',
  Reading = 'Reading',
  Slam = 'Slam',
  Social_Gathering = 'Social Gathering',
  Writers_Conference = 'Writers Conference',
}

export enum COUPON_TYPES {
  USER = 'User',
  GENERAL = 'General',
}

export enum CARD_TYPES {
  VISA = 'Visa',
  MASTERCARD = 'MasterCard',
  AMERICAN_EXPRESS = 'American Express',
  DISCOVER = 'Discover',
  DINERS_CLUB = 'Diners Club',
  JCB = 'JCB',
  UNION_PAY = 'UnionPay',
}

export enum EVENT_TYPES {
  HYBRID = 'Hybrid',
  IN_PERSON = 'In Person',
  ONLINE = 'Online',
}

export enum CLASS_TYPES {
  YOUTH = 'Youth Class',
}

export enum MAILING_LIST_TYPES {
  PARENTS = 'All Parents',
  STUDENTS = 'All Students',
}

export enum EVENT_TYPES {
  EVENT = 'Event',
  OUTREACH = 'Outreach',
}

export enum CLASS_EVENT_TYPE {
  YOUTH = 'Youth Class',
}
