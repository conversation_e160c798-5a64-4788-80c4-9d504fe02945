interface Messages {
  resultCode: 'Ok' | 'Error'
  message: MessagesMessage[]
}

interface MessagesMessage {
  code: string
  text: string
}

interface ErrorMessage {
  errorCode: string
  errorText: string
}

interface TransactionResponse {
  responseCode: string
  authCode: string
  avsResultCode: string
  cvvResultCode: string
  cavvResultCode: string
  transId: string
  refTransID: string
  transHash: string
  testRequest: string
  accountNumber: string
  accountType: string
  messages: TransactionResponseMessage[]
  transHashSha2: string
  SupplementalDataQualificationIndicator: number
  networkTransId: string
  errors: ErrorMessage[]
}

interface CustomerProfile {
  paymentProfiles: PaymentProfile[]
  profileType: string
  customerProfileId: string
  merchantCustomerId: string
}

interface PaymentProfile {
  customerPaymentProfileId: string
  payment: Payment
  customerType?: string
}

interface Payment {
  creditCard: CreditCard
}
interface CreditCard {
  cardNumber: string
  expirationDate: string
  cardType: string
}

export interface CreateCustomerProfileResponse {
  customerProfileId: string
  customerPaymentProfileIdList: string[]
  customerShippingAddressIdList: any[]
  validationDirectResponseList: string[]
  messages: Messages
}

export interface CreateCustomerPaymentProfileResponse {
  customerProfileId: string
  customerPaymentProfileId: string
  validationDirectResponse: string
  messages: Messages
}

export interface GetCustomerProfileResponse {
  profile: CustomerProfile
  messages: Messages
}

export interface DeleteCustomerPaymentProfileResponse {
  messages: Messages
}

export interface ChargeCreditCardResponse {
  transactionResponse: TransactionResponse
  messages: Messages
}

export interface ChargeCustomerProfileResponse {
  transactionResponse: TransactionResponse
  messages: Messages
}

export interface ChargeCreditCardRecurringResponse {
  subscriptionId: string
  messages: Messages
}

export interface CancelRecurringResponse {
  messages: Messages
}

interface TransactionResponseMessage {
  code: string
  description: string
}
