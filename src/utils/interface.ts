import { ICONS } from './constants'

type IconKey = keyof typeof ICONS

export interface SideNavItem {
  title: string
  link: string
  icon: IconKey
  subItems?: {
    title: string
    link: string
  }[]
}

export interface PriceDetails {
  userId: string
  entityId: string
  entityType: string
  amount: number
  walletBalance: number
  iat: number
  discountedAmount: number
  paymentType: string
  isWalletEnabled: boolean
}
