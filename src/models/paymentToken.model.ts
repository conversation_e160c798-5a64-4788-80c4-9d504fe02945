import mongoose, { Document, Model } from 'mongoose'

type PaymentType = 'One-time' | 'Subscription'
export type Schedule = 'Monthly' | 'Annual'

export interface IPaymentToken {
  userId: string
  token: string
  status: 'active' | 'completed' | 'failed'
  paymentType: PaymentType
  occurrences: number
  schedule?: Schedule
  amount: number
}

export interface IPaymentTokenDocument extends IPaymentToken, Document {
  userId: string
  token: string
  status: 'active' | 'completed' | 'failed'
  paymentType: PaymentType
  occurrences: number
  schedule?: Schedule
  amount: number

  createdAt: Date
  updatedAt: Date
}

export interface IPaymentTokenSFDocument {
  Id: string,
  User_Id__c: string,
  Token__c: string,
  Status__c: 'active' | 'completed' | 'failed',
  Payment_Type__c: PaymentType,
  Occurences__c: number,
  Schedule__c?: Schedule,
  Amount__c: number,

  CreatedDate: Date,
  LastModifiedDate: Date,
}
